import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/local_notification/local_notification.dart';
import 'package:flutter_mixed/app/permission/permission_collection_dialog.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtil {
  static Future<PermissionStatus> requestContactsPermissionStatus() async {
    return await Permission.contacts.request();
  }

  static Future<Map<Permission, PermissionStatus>>
      requestCameraAndPhotoPermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Platform.isAndroid? Permission.storage:Permission.photos,
    ].request();
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>>
      requestCameraPermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
    ].request();
    int type = 0;//0无权限 1 有权限
    if (statuses.values.contains(PermissionStatus.granted)) {
      //有权限
    }
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>>
      requestPhotoPermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Platform.isAndroid? Permission.storage:Permission.photos,
    ].request();
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>>
      requestBasePermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.phone,
      Permission.storage,
    ].request();
    return statuses;
  }

  static Future<bool> requestFullFileAccess(BuildContext context) async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    var sdkInit =  androidInfo.version.sdkInt; // 返回 SDK_INT
    if(sdkInit < 30) {
      return await checkStoragePermission(context , tip: '需要您同意使用存储权限，才能使用发送文件的功能');
    };
    final status = await Permission.manageExternalStorage.request();
    if (status.isGranted) {
      return true;
    } else if (status.isPermanentlyDenied) {
      await openAppSettings();
      return false;
    } else {
      return false;
    }
  }


  static Future<Map<Permission, PermissionStatus>>
      requestLocationPermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.locationWhenInUse,
    ].request();
    return statuses;
  }
  static Future<Map<Permission, PermissionStatus>>
  requestMicAndStoragePermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.microphone,
      Permission.storage,
    ].request();
    return statuses;
  }

  static Future requestNotificationPermission() async {
    // LocalNotification().getFlutterLocalNotificationsPlugin().requestNotificationPermission();
  }

  // 图片上传申请权限
  static showPhotoPrivacyDialog(BuildContext context,
      {String? tip, Function? callback}) {
    showSimpleDialog(context,
        title: tip ?? "需要你授权使用存储卡和相机权限才能进行图片上传等操作",
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
      if (callback != null) {
        callback();
      }
    });
  }

  // 图片上传申请权限
  static showBasePermissionDialog(BuildContext context, {Function? callback , String? tip}) {
    showSimpleDialog(context,
        title: tip ?? "需要你授权使用存储卡等权限才能正常使用担当app",
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
      if (callback != null) {
        callback();
      }
    });
  }

  static showCameraPrivacyDialog(BuildContext context, {Function? callback}) {
    showSimpleDialog(context,
        title: '需要你授权使用拍照权限才能进行图片上传',
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
      if (callback != null) {
        callback();
      }
    });
  }

  static showLocationPrivacyDialog(BuildContext context, {Function? callback}) {
    showSimpleDialog(context,
        title: '位置权限',
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
      if (callback != null) {
        callback();
      }
    });
  }

  static showContactsPrivacyDialog(BuildContext context,
      {VoidCallback? callback, VoidCallback? cancelCallBack}) {
    showSimpleDialog(context,
        title: '为支持“添加联系人”功能，担当会收集通讯录数据。通信录会经过加密处理，不会分享给任何人或机构。',
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
      if (callback != null) {
        callback();
      }
    });
  }
  // 发送语音申请权限
  static showAudioPrivacyDialog(BuildContext context,
      {String? tip, Function? callback}) {
    showSimpleDialog(context,
        title: tip ?? "需要你授权使用存储卡，麦克风等权限才能进行发送语音消息等操作",
        cancelText: '拒绝',
        confirmText: '允许', confirmCallBack: () {
          if (callback != null) {
            callback();
          }
        });
  }


  // 为方便将camera和photo权限一起申请
  static Future<bool> checkPhotoPermission(BuildContext context) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.androidInfo;

      bool isDenied = false;
      if ((deviceInfo.version.sdkInt ?? 30) >= 33) {
        var value = await Future.wait([Permission.camera.status]);
        isDenied = (value[0] == PermissionStatus.denied);
      } else {
        var value = await Future.wait(
            [Permission.camera.status, Permission.storage.status , Permission.photos.status]);
        isDenied = (value[0] == PermissionStatus.denied ||
            value[1] == PermissionStatus.denied);
      }

      if (isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context, callback: () {
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      c.complete(true);
    }
    return c.future;
  }

  static Future<bool> checkStoragePermission(BuildContext context,{String? tip}) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      var value = await Future.wait([Permission.storage.status]);
      if (value[0] == PermissionStatus.denied) {
        PermissionUtil.showBasePermissionDialog(context, tip: tip, callback: () {
          PermissionUtil.requestBasePermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      c.complete(true);
    }
    return c.future;
  }

  static Future<bool> checkCameraPermission(BuildContext context,
      {String? tip}) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.androidInfo;

      bool isDenied = false;
      if ((deviceInfo.version.sdkInt ?? 30) >= 33) {
        var value = await Future.wait([Permission.camera.status]);
        isDenied = (value[0] == PermissionStatus.denied);
      } else {
        var value = await Future.wait(
            [Permission.camera.status, Permission.storage.status]);
        isDenied = (value[0] == PermissionStatus.denied ||
            value[1] == PermissionStatus.denied);
      }

      if (isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context, tip: tip, callback: () {
          PermissionUtil.requestCameraAndPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      bool isDenied = false;
      var value = await Future.wait([Permission.camera.status]);
      isDenied = (value[0] == PermissionStatus.permanentlyDenied);
      if (isDenied) {
        toast('请从手机设置中打开相机权限');
        c.complete(false);
      } else {
        c.complete(true);
      }
    }
    return c.future;
  }


  static Future<bool> checkLocationPermission(BuildContext context,
      {String? tip = '需要你授权使用定位权限才能使用地图功能'}) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      bool isDenied = false;
      var value = await Future.wait([Permission.location.status]);
      isDenied = (value[0] == PermissionStatus.denied);

      if (isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context, tip: tip, callback: () {
          PermissionUtil.requestLocationPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      bool isDenied = false;
      var value = await Future.wait([Permission.location.status]);
      isDenied = (value[0] == PermissionStatus.permanentlyDenied);
      if (isDenied) {
        toast('请从手机设置中打开定位权限');
        c.complete(false);
      } else {
        c.complete(true);
      }
    }
    return c.future;
  }


  static checkGalleryPermission(BuildContext context, {String? tip}) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      bool isDenied = false;

      var value = await Future.wait([Permission.storage.status]);
      isDenied = (value[0] == PermissionStatus.denied);

      if (isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context, tip: tip, callback: () {
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      bool isDenied = false;
      var value = await Future.wait([Permission.photos.status]);
      isDenied = (value[0] == PermissionStatus.permanentlyDenied);
      if (isDenied) {
        toast('请从手机设置中打开照片权限');
        c.complete(false);
      } else {
        PermissionUtil.requestPhotoPermission().then((map){
          if (map.values.contains(PermissionStatus.permanentlyDenied)) {
            c.complete(false);
          }else{
            c.complete(true);
          }

        });

      }
    }
    return c.future;
  }
  // 录音权限
  static Future<bool> checkAudioPermission(BuildContext context) async {
    Completer<bool> c = Completer();
    if (Platform.isAndroid) {
      bool isDenied = false;
      var value = await Future.wait(
          [Permission.microphone.status, Permission.storage.status]);
      isDenied = (value[0] == PermissionStatus.denied ||
          value[1] == PermissionStatus.denied);

      if (isDenied) {
        PermissionUtil.showAudioPrivacyDialog(context, callback: () {
          PermissionUtil.requestMicAndStoragePermission().then((micP) {
            var _grant = true;
            micP.forEach((key, value) {
              if (value.isDenied) {
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      } else {
        c.complete(true);
      }
    } else {
      c.complete(true);
    }
    return c.future;
  }

  static listenerCamera(Function call) async{
    await Permission.camera.onDeniedCallback(() {
      // Your code
     call(true);
    }).onPermanentlyDeniedCallback(() {
      // Your code
      call(true);
    }).request();
  }
}


var scanPermissionTip = "需要你授权使用存储卡，相机等权限才能进行扫码操作";
var takePhotoPermissionTip = "需要你授权使用存储卡才能进行上传图片等操作";
var cameraPhotoPermissionTip = "需要你授权使用存储卡，相机等权限才能进行拍照上传等操作";
var recordAudioPermissionTip = "需要你授权使用存储卡，麦克风等权限才能进行发送语音消息等操作";
