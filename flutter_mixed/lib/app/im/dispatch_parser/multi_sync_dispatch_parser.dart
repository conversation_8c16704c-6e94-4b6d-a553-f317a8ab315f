import 'package:fast_contacts/fast_contacts.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/im/db/db_helper.dart';
import 'package:flutter_mixed/app/im/db/entity/session.dart';
import 'package:flutter_mixed/app/im/ext/im_msg_ext.dart';
import 'package:flutter_mixed/app/im/ext/session_ext.dart';
import 'package:flutter_mixed/app/im/im_send_helper.dart';
import 'package:flutter_mixed/app/im/refresh_pages_data/refresh_pages_data.dart';
import 'package:flutter_mixed/app/im/ui/chat/chat_controller.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

import '../constant/ImMsgConstant.dart';
import '../convert/multi_synchronize_msg.dart';
import '../proto_model/Msg.pb.dart';
import 'multi_sync_event_model.dart';

class MultiSyncParser {
  String ext1;
  bool selfMsg;
  int type;

  MultiSyncParser(this.ext1, this.selfMsg, this.type);

  void parseData() async {
    var myUserId = await UserHelper.getUid();
    if (selfMsg == true &&
        type == ConstantImMsgType.SSChatMessageTypeMoreLoginNoticeChange) {
      var multilMsg = convert2MultiSynchronizeMsgBean(ext1);
      switch (multilMsg.handleType) {
        case ConstantImMsgType.SSChatMessageHandleTypeAnswer:
          Channel().invoke(Channel_Receive_Call_Msg,
              {'msgType': type, 'ext': multilMsg.toJson()});
          break;
        case ConstantImMsgType.SSChatMessageHandleTypeHangup:
          Channel().invoke(Channel_Receive_Call_Msg,
              {'msgType': type, 'ext': multilMsg.toJson()});
          break;
        case ConstantImMsgType.SSChatMessageHandleTypeSessionUnread:
          //单聊群聊清空未读数
          var pcSessionId = multilMsg.handleId;
          var session = await DbHelper.getSessionByOwnerId2SessionId(
              myUserId, pcSessionId ?? '');
          if(session != null){
            if(session.isSingleChat() || session.isGroupChat()){
               DbHelper.upDateSessionUnReadCount(myUserId, pcSessionId ?? '', 0);
            }
          }


          break;
        case ConstantImMsgType.SSChatMessageHandleTypeSingleSynchronization:
          //pc端创建新的会话时，这里接收到这个消息，也在移动端创建会话
          if(multilMsg.sessionType == ConstantImMsgType.SSChatConversationTypeGroupChat) return;
          var pcSessionId = multilMsg.handleId;
          var pcUserId = multilMsg.appChatId;
          var pcName = multilMsg.name;
          var pcHeaderUrl = multilMsg.headerUrl;
          Session? session = await DbHelper.getSessionByOwnerId2SessionId(
              myUserId, pcSessionId ?? '');
          if (session == null) {
            Map dataDic = {
              'userId': pcUserId,
              'imId': pcSessionId,
              'name': pcName,
              'headimg': pcHeaderUrl
            };
            dataDic.createSinglePageParam();
          }
          break;
        case ConstantImMsgType.SSChatMessageHandleTypeThreeNoticeDisturb:
          //多端同步： 系统、工作、审批、工单通知的消息免打扰设置同步
          var sessionId = multilMsg.handleId ?? '';
          var isOpen = multilMsg.switchStatus ?? 0;
          var handleId = multilMsg.handleId ?? '';

          /*if (multilMsg.handleId == ConstantTcpUtil.TCP_SYSTEM_SESSIONID) {
            //系统通知
          } else if (multilMsg.handleId ==
              ConstantTcpUtil.TCP_APPROVAL_SESSIONID) {
            // 审批通知
          } else if (multilMsg.handleId ==
              ConstantTcpUtil.TCP_TICKET_SESSIONID) {
            // 工单通知
          } else if (multilMsg.handleId ==
              ConstantTcpUtil.TCP_MATTER_SESSIONID) {
            // 库存
          } else {
            // 工作通知
          }*/

          sendSessionMsgDisturb(sessionId, isOpen == 1, handleId);

          break;

        case ConstantImMsgType.SSChatMessageHandleTypeConversationTop:
          // 多端同步：在线收到指令增加 handleType 6单聊群聊置顶同步指令 handleId为sessionId  switchStatus  0关闭  1开启
          var sessionId = multilMsg.handleId ?? '';
          var isTop = multilMsg.switchStatus ?? 0;
          // 1 通知session列表； 2 通知设置页面
          sendSessionMsgTopEvent(sessionId, isTop == 1);
          eventBus.fire(EventChatInfoMsgTop(sessionId, isTop == 1));

          break;

        case ConstantImMsgType.SSChatMessageHandleTypeApproveRedPoint:
          //刷新审批红点
          RefreshPagesData.refreshApproveUnreadData(
              {'handleId': multilMsg.handleId});
          break;
        default:
          break;
      }
    }
  }
}

// 通知session列表更新置顶状态
void sendSessionMsgTopEvent(String sessionId, bool isTop) {
  eventBus.fire(EventSessionMsgTop(sessionId, isTop));
}

// 通知session列表更新免打扰状态
void sendSessionMsgDisturb(
    String sessionId, bool disturbOpen, String handleId) {
  logger('sendSessionMsgDisturb..... $sessionId , $disturbOpen');
  eventBus.fire(EventSessionMsgDisturb(sessionId, disturbOpen, handleId));
}

//通知chat页面提示
sendMsgForChat(String? sessionId,String? warning){
  if (StringUtil.isEmpty(sessionId) || StringUtil.isEmpty(warning)) return;
  bool isRegistered = Get.isRegistered<ChatController>(tag: sessionId);
  if (isRegistered) {
    ChatController? chatController = Get.find<ChatController>(tag: sessionId);
    chatController.receiveWarning(warning!);
  }
}
