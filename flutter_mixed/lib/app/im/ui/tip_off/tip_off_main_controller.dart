import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../common/api/LoginApi.dart';
import '../../request/datasource/relation_datasource.dart';
import '../../request/entity/tip_off_resp.dart';

class TipOffMainController extends GetxController {

  List<TipOffItemResp> uiList = [];

  String sessionId = '';

  @override
  void onInit() {
    super.onInit();
    sessionId = Get.arguments['sessionId'] ?? '';
    _fetchTipOffList();
  }

  _fetchTipOffList() async {
    var datasource = RelationDatasource(retrofitDio , baseUrl: Host.HOST);
    var resp = await datasource.getComplainContentList();
    if(resp.success()){
      uiList.clear();
      uiList.addAll(resp.data);
      update();
    }
  }
}