import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';

import 'package:get/get.dart';

import '../controllers/org_verify_controller.dart';
import '../../../../../common/widgets/widgets.dart';

class OrgVerifyView extends GetView<OrgVerifyController> {
  const OrgVerifyView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(()=>Scaffold(
      backgroundColor: ColorConfig.backgroundColor,
      appBar: TitleBar().backAppbar(context, controller.type.value == 1? '好友请求' : '验证申请', false, [
        Container(
          width: 72,
          padding: const EdgeInsets.fromLTRB(16, 7, 16, 7),
          child: CupertinoButton(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              pressedOpacity: 0.5,
              borderRadius: BorderRadius.circular(6),
              color: ColorConfig.themeCorlor,
              child: const Text(
                '发送',
                style: TextStyle(color: ColorConfig.whiteColor, fontSize: 14),
              ),
              onPressed: () {
                if (controller.type.value == 0) {
                  controller.applyJoinOrg(controller.msgController.text);
                } else {
                  controller.addFriend();
                }
              }),
        )
      ], onPressed: () {
        Get.back();
      }),
      body: Column(
        children: [
          8.gap,
          Container(
            alignment: Alignment.centerLeft,
            color: ColorConfig.backgroundColor,
            height: 22,
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: Obx(() => Text(
              controller.getTitle(),
              textAlign: TextAlign.left,
              style: const TextStyle(
                color: ColorConfig.desTextColor,
                fontSize: 12,
              ),
            )),
          ),
          4.gap,
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: ColorConfig.whiteColor
            ),
            child: Column(
              children: [
                Container(
                  height: 80,
                  padding: const EdgeInsets.all(0),
                  child: TextField(
                    onSubmitted: (value) {},
                    onChanged: (value) {
                      controller.type.refresh();
                    },
                    controller: controller.msgController,
                    maxLines: null,
                    minLines: 1,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(30)
                  ],
                    textInputAction: TextInputAction.done,
                    style: const TextStyle(
                      color: ColorConfig.mainTextColor,
                      fontSize: 14,
                    ),
                    decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                      border: OutlineInputBorder(borderSide: BorderSide.none),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.centerRight,
                  child: Text(
                    '${controller.msgController.text.length}/30',
                    textAlign: TextAlign.right,
                    style: const TextStyle(
                      color: ColorConfig.desTextColor,
                      fontSize: 12,
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    ));
  }
}
