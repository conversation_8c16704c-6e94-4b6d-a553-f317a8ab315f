import 'dart:convert';
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/dialog/choosepicker-dialog.dart';
import 'package:flutter_mixed/app/common/dialog/datepicker-dialog.dart';
import 'package:flutter_mixed/app/common/widgets/app_scaffold.dart';
import 'package:flutter_mixed/app/common/widgets/image_loader.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/external_model.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/org_model.dart';
import 'package:flutter_mixed/app/modules/workStand/common/focus_switch_widget.dart';
import 'package:flutter_mixed/app/modules/workStand/models/appprove_corg_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/form_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/procee_model.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';

import 'package:get/get.dart';

import '../controllers/send_approve_controller.dart';

class SendApproveView extends StatefulWidget {
  bool showFocusBar = false;

  @override
  State<StatefulWidget> createState() => _SendApproveState();
}

class _SendApproveState extends State<SendApproveView>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return SendApproveView1(showFocusbar: widget.showFocusBar);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if(mounted){
        setState(() {
          if (MediaQuery.of(context).viewInsets.bottom == 0) {
            //关闭键盘
            widget.showFocusBar = false;
          } else {
            //显示键盘
            widget.showFocusBar = true;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}

// 发起审批
class SendApproveView1 extends GetView<SendApproveController> {
  bool showFocusbar;

  SendApproveView1({this.showFocusbar = false});

  @override
  Widget build(BuildContext context) {
    return Obx(() => AppScaffold(
        body: SafeArea(
          bottom: false,
          child: Column(
            children: [
              Expanded(
                  child: ListView.builder(
                      itemCount: controller.dataList.length +
                          controller.processModelFormList.length +
                       2,
                      itemBuilder: (context, index) {
                        if(index == 0){
                          return InkWell(
                              onTap: () {
                                List nameList = [];
                                for (var i = 0;
                                    i < controller.corgIdList.length;
                                    i++) {
                                  AppproveCorgModel? corgModel =
                                      controller.corgIdList[i];
                                  if (!StringUtil.isEmpty(corgModel?.orgName)) {
                                    nameList.add(corgModel?.orgName);
                                  }
                                }
                                if (nameList.length <= 1) return;
                                SettingWidget().showCupertinoActionSheetForPage(
                                    context, nameList, (value) {
                                  if (controller.currentCorgModel?.corgId !=
                                      controller.corgIdList[value]?.corgId) {
                                    controller.currentCorgModel =
                                        controller.corgIdList[value];
                                    controller.getRequiredContentIsDone();
                                    controller.dataList.refresh();
                                  }
                                });
                              },
                                  child: backWidgetForChoose(
                                      '归属公司',
                                      controller.currentCorgModel?.orgName ?? '',
                                      '0',isHiddenArrow: controller.corgIdList.length == 1),
                              );
                        }
                        else if (index < controller.dataList.length + 1) {
                          List modelList = controller.dataList[index - 1];
                          return Column(
                            children: [

                              Offstage(
                                offstage: !(controller.deptList.length > 1 &&
                                    index == 1),
                                child: InkWell(
                                  onTap: () {
                                    List nameList = [];
                                    for (var i = 0;
                                    i < controller.deptList.length;
                                    i++) {
                                      Map deptMap = controller.deptList[i];
                                      nameList.add(deptMap['name']);
                                    }
                                    SettingWidget()
                                        .showCupertinoActionSheetForPage(
                                        context, nameList, (value) {
                                      controller.currentDeptDic =
                                      controller.deptList[value];
                                      controller.dataList.refresh();
                                    });
                                  },
                                  child: backWidgetForChoose(
                                      '部门',
                                      controller.currentDeptDic['name'] ?? '',
                                      '0'),
                                ),
                              ),
                              Container(
                                height: 50,
                                alignment: Alignment.centerLeft,
                                padding: EdgeInsets.only(
                                    left: 15, right: 15, top: 10),
                                child: Text(
                                  controller.sendModelName[index - 1],
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Column(
                                children: backWidgetWithFromData(modelList),
                              )
                            ],
                          );
                        } else if (index <
                            controller.dataList.length +
                                controller.processModelFormList.length + 1) {
                          List modelList = controller.processModelFormList[
                          index - controller.dataList.length - 1];
                          return Column(
                            children: [
                              Container(
                                height: 50,
                                alignment: Alignment.centerLeft,
                                padding: EdgeInsets.only(
                                    left: 15, right: 15, top: 10),
                                child: Text(
                                  controller.processModelName[
                                  index - controller.dataList.length - 1],
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Listener(
                                onPointerUp: (event) {
                                  toast('办理人填写的表单，无需填写');
                                },
                                child: AbsorbPointer(
                                  child: Column(
                                    children:
                                    backWidgetWithFromData(modelList),
                                  ),
                                ),
                              )
                            ],
                          );
                        } else if (index ==
                            controller.dataList.length +
                                controller.processModelFormList.length + 1) {
                          return Column(
                            children: controller.processList.isEmpty
                                ? [
                              SizedBox(
                                height: 8,
                              ),
                              Container(
                                width: double.infinity,
                                height: 24,
                                padding: EdgeInsets.only(
                                    left: 15, right: 15),
                                child: Text(
                                  '审批流程',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Container(
                                width: double.infinity,
                                height: 22,
                                padding: EdgeInsets.only(
                                    left: 15, right: 15),
                                child: Text(
                                  '必须填信息填写完整后，将显示审批流程',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              )
                            ]
                                : backProcessWidget(),
                          );
                        } else {
                          return Container();
                        }
                      })),
              FocusSwitchBar(
                showBar: showFocusbar,
                preFocus: () {
                  controller.lastFocus();
                },
                nextFocus: () {
                  controller.nextFocus();
                },
              ),
              Container(
                width: double.infinity,
                color: ColorConfig.whiteColor,
                padding: EdgeInsets.fromLTRB(
                    16, 16, 16, DeviceUtils().bottom.value + 16),
                child: CupertinoButton(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    color: ColorConfig.themeCorlor,
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                    pressedOpacity: 0.5,
                    child: Text(
                      '提交',
                      style: TextStyle(
                          color: ColorConfig.whiteColor, fontSize: 16),
                    ),
                    onPressed: () async {
                      controller.jugeDataIsDone();
                    }),
              )
            ],
          ),
        ),));
  }

  backProcessWidget() {
    List<Widget> lists = [];
    lists.add(Column(
      children: [
        Container(
          width: double.infinity,
          height: 54,
          padding: EdgeInsets.only(left: 16, right: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  '审批流程',
                  style:
                      TextStyle(fontSize: 16, color: ColorConfig.mainTextColor),
                ),
              )
            ],
          ),
        ),
        Divider(
          height: 1,
          color: ColorConfig.lineColor,
        ),
      ],
    ));
    for (var i = 0; i < controller.processList.length + 1; i++) {
      if (i == controller.processList.length) {
        lists.add(Container(
          width: double.infinity,
          height: 24,
          color: ColorConfig.whiteColor,
          padding: EdgeInsets.only(left: 8, right: 16),
          child: Row(
            children: [
              Container(
                alignment: Alignment.center,
                width: 16,
                height: 24,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                      border:
                          Border.all(width: 1, color: ColorConfig.lineColor),
                      borderRadius: BorderRadius.circular(4)),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                  child: Container(
                alignment: Alignment.centerLeft,
                height: 24,
                child: Text(
                  '结束',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ))
            ],
          ),
        ));
      } else {
        ProcessModel model = controller.processList[i];
        lists.add(backFinishProcess(model));
      }
    }
    return lists;
  }

  backFinishProcess(ProcessModel model) {
    return Column(
      children: [
        Container(
          color: ColorConfig.whiteColor,
          constraints: BoxConstraints(minHeight: 90),
          width: double.infinity,
          padding: EdgeInsets.only(left: 8, right: 16),
          child: IntrinsicHeight(
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 16,
                        height: 24,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: ColorConfig.lineColor),
                              borderRadius: BorderRadius.circular(4)),
                        ),
                      ),
                      Expanded(
                          child: Container(
                        width: 1,
                        height: double.infinity,
                        child: DashedLine(
                          height: 3,
                          color: ColorConfig.lineColor,
                        ),
                      ))
                    ],
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                    child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      height: 24,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            alignment: Alignment.centerLeft,
                            height: 24,
                            child: Text(
                              model.nodeName!,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                            ),
                          ),
                          Offstage(
                            offstage: model.isSubmitterChoose == 0,
                            child: InkWell(
                              onTap: () async {
                                if (controller.isAllowExternalAssignee &&
                                    model.scope == 1) {
                                  SettingWidget()
                                      .showCupertinoActionSheetForPage(
                                          Get.context!, ['企业成员', '外部联系人'],
                                          (value) async {
                                    if (value == 0) {
                                      List modelList = [];

                                      for (var i = 0;
                                          i < model.assigneeList.length;
                                          i++) {
                                        Map<String, dynamic> userMap =
                                            model.assigneeList[i];

                                        modelList
                                            .add(MemberModel.fromJson(userMap));
                                      }
                                      var result = await Get.toNamed(
                                          '/choose-all-org-members',
                                          arguments: {
                                            'model': controller.orgModel,
                                            'type': 1,
                                            'chooseType':
                                                model.chooseType == 1 ? 2 : 1,
                                            'selectList': modelList
                                          });
                                      if (result != null &&
                                          result['list'] != null) {
                                        model.assigneeList.clear();
                                        List memeList = result['list'];
                                        for (var i = 0;
                                            i < memeList.length;
                                            i++) {
                                          MemberModel memeberModel =
                                              memeList[i];

                                          model.assigneeList
                                              .add(memeberModel.toJson());
                                        }
                                      }
                                    } else {
                                      List modelList = [];

                                      for (var i = 0;
                                          i < model.assigneeList.length;
                                          i++) {
                                        Map<String, dynamic> userMap =
                                            model.assigneeList[i];
                                        modelList.add(
                                            ExternalModel.fromJson(userMap));
                                      }
                                      var result = await Get.toNamed(
                                          '/external-contacts',
                                          arguments: {
                                            'selectList': modelList,
                                            'model': controller.orgModel,
                                            'chooseType':
                                                model.chooseType == 1 ? 2 : 1
                                          });

                                      if (result != null &&
                                          result['list'] != null) {
                                        model.assigneeList.clear();
                                        List memeList = result['list'];
                                        for (var i = 0;
                                            i < memeList.length;
                                            i++) {
                                          ExternalModel memeberModel =
                                              memeList[i];

                                          model.assigneeList
                                              .add(memeberModel.toJson());
                                        }
                                      }
                                    }
                                    controller.dataList.refresh();
                                  });
                                } else {
                                  List dataList = model.memberList
                                      .map((e) => MemberModel.fromJson(e))
                                      .toList();
                                  List modelList = [];

                                  for (var i = 0;
                                      i < model.assigneeList.length;
                                      i++) {
                                    Map<String, dynamic> userMap =
                                        model.assigneeList[i];
                                    modelList
                                        .add(MemberModel.fromJson(userMap));
                                  }
                                  if (model.scope == 1) {
                                    var result = await Get.toNamed(
                                        '/choose-all-org-members',
                                        arguments: {
                                          'model': OrgModel(controller.orgId),
                                          'type': 1,
                                          'chooseType':
                                              model.chooseType == 1 ? 2 : 1,
                                          'selectList': modelList
                                        });

                                    if (result != null &&
                                        result['list'] != null) {
                                      List memeList = result['list'];

                                      model.assigneeList.clear();
                                      for (var i = 0;
                                          i < memeList.length;
                                          i++) {
                                        MemberModel memeberModel = memeList[i];

                                        model.assigneeList
                                            .add(memeberModel.toJson());
                                      }
                                    }
                                  } else {
                                    //选择指定成员

                                    var result = await Get.toNamed(
                                        Routes.APPROVE_CHOOSE_OPTION,
                                        arguments: {
                                          'chooseCount':
                                              model.chooseType == 1 ? 2 : 1,
                                          'list': dataList,
                                          'selectList': modelList,
                                          'type': 1
                                        });
                                    if (result != null &&
                                        result['list'] != null) {
                                      model.assigneeList.clear();
                                      List memeList = result['list'];
                                      for (var i = 0;
                                          i < memeList.length;
                                          i++) {
                                        MemberModel memeberModel = memeList[i];

                                        model.assigneeList
                                            .add(memeberModel.toJson());
                                      }
                                    }
                                  }
                                  controller.dataList.refresh();
                                }
                              },
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: Image.asset(
                                    'assets/images/3.0x/workflow_function_add.png'),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Column(
                      children: backUserListContent(model),
                    )
                  ],
                ))
              ],
            ),
          ),
        )
      ],
    );
  }

  backUserListContent(ProcessModel model) {
    List<Widget> lists = [];

    if (model.assigneeList.isEmpty) {
      lists.add(Column(
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 22,
            child: Text(
              model.handleType == 1
                  ? '请选择审批人'
                  : model.handleType == 2
                      ? '请选择办理人'
                      : '请选择抄送人',
              style: TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
            ),
          ),
          SizedBox(
            height: 36,
          )
        ],
      ));
    } else {
      for (var i = 0; i < model.assigneeList.length; i++) {
        lists.add(backFinishContent(model, i));
      }
    }
    lists.add(SizedBox(
      height: 16,
    ));
    return lists;
  }

  backFinishContent(ProcessModel model, int index) {
    Map userDic = model.assigneeList[index];
    return Column(
      children: [
        SizedBox(
          height: 8,
        ),
        Container(
          width: double.infinity,
          height: 40,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: DecorationImage(
                        image: SettingWidget.backImageProvider(userDic['headimg'] ?? userDic['avatar']))),
              ),
              SizedBox(
                width: 8,
              ),
              Container(
                child: Text(
                  userDic['name'],
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Offstage(
                offstage: model.isSubmitterChoose == 0,
                child: InkWell(
                  onTap: () {
                    model.assigneeList.removeAt(index);
                    controller.dataList.refresh();
                  },
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset(
                        'assets/images/3.0x/approve_deleteUser.png'),
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  backWidgetWithFromData(List modelList) {
    List<Widget> lists = [];

    for (var i = 0; i < modelList.length; i++) {
      FormWidetModel model = modelList[i];
      Widget widget = Container();
      if (model.type == 1) {
        widget = backWidgetForShortText(model);
      } else if (model.type == 2) {
        widget = backWidgetForLongText(model);
      } else if (model.type == 3 || model.type == 4) {
        widget = backWidgetForTime(model);
      } else if (model.type == 5 || model.type == 15) {
        //时间长度类5旧版 15新版
        widget = InkWell(
          onTap: () async {
            List dataList = [];

            List dayList = [];
            for (var i = 0; i < 365; i++) {
              dayList.add('$i天');
            }
            List hourList = [];
            for (var i = 0; i < 24; i++) {
              hourList.add('$i小时');
            }
            List minList = [];
            for (var i = 0; i < 60; i++) {
              minList.add('$i分钟');
            }
            if (model.dateChoose == 0) {
              dataList.add(dayList);
              dataList.add(hourList);
              dataList.add(minList);
            }
            if (model.dateChoose == 1) {
              dataList.add(dayList);
            }
            if (model.dateChoose == 2) {
              dataList.add(hourList);
              dataList.add(minList);
            }
            ChoosePickerDialog(dataList, model.contentList)
                .show((List indexList) {
              print('indexList---$indexList');
              bool isZ = true;
              for (var i = 0; i < indexList.length; i++) {
                if (indexList[i] > 0) {
                  isZ = false;
                }
              }
              if (isZ) {
                toast('请选择时长');
                return;
              } else {
                model.showContent = '';
                List showList = ['天', '小时', '分钟'];
                model.contentList = indexList;
                if (model.dateChoose == 0) {
                  model.showContent =
                      '${indexList[0]}天${indexList[1]}小时${indexList[2]}分钟';
                }
                if (model.dateChoose == 1) {
                  model.showContent = '${indexList[0]}天';
                }
                if (model.dateChoose == 2) {
                  model.showContent = '${indexList[0]}小时${indexList[1]}分钟';
                }

                if (model.type == 5) {
                  model.content = model.showContent;
                }
              }
              controller.dataList.refresh();
              controller.getRequiredContentIsDone();
              controller.dealCacheData();
            });
          },
          child: backWidgetForChoose(
              model.title ?? '',
              model.showContent.isEmpty ? (model.prompt ?? '') : model.showContent,
              model.required!),
        );
      } else if (model.type == 6 || model.type == 16) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed('/approve-choose-option', arguments: {
              'chooseCount': 1,
              'list': model.chooseFrameList,
              'selectList': model.contentList,
              'type': 0
            });

            if(result != null){
              model.contentList = result['list'];
              Map contentMap = model.contentList[0];
              if (model.type == 6) {
                model.content = contentMap['title'];
                model.showContent = contentMap['title'];
              }
              if (model.type == 16) {
                model.showContent = contentMap['title'];
                model.content = contentMap['frameId'];
              }

              controller.dataList.refresh();
              controller.getRequiredContentIsDone();
              controller.dealCacheData();
            }
          },
          child: backWidgetForChoose(
              model.title ?? '',
              model.showContent.isEmpty ? (model.prompt ?? '') : model.showContent,
              model.required!),
        );
      } else if (model.type == 7 || model.type == 17) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed(Routes.APPROVE_CHOOSE_OPTION, arguments: {
              'chooseCount': model.chooseCount,
              'list': model.chooseFrameList,
              'selectList': model.contentList,
              'type': 0
            });
            model.contentList = result['list'];

            List titleList = [];
            for (var i = 0; i < model.contentList.length; i++) {
              Map contentMap = model.contentList[i];
              titleList.add(contentMap['title']);
            }
            model.showContent = titleList.join('、');
            if (model.type == 7) {
              model.content = titleList.join('、');
            }

            controller.dataList.refresh();
            controller.getRequiredContentIsDone();
            controller.dealCacheData();
          },
          child: backWidgetForChoose(
              model.title ?? '',
              model.showContent.isEmpty ? (model.prompt ?? '') : model.showContent,
              model.required!),
        );
      } else if (model.type == 8) {
        widget = InkWell(
          onTap: () async {
            var result =
                await Get.toNamed('/choose-all-org-members', arguments: {
              'model': OrgModel(controller.orgId),
              'type': 1,
              'chooseType': model.personType! + 1,
              'selectList': List.from(model.contentList)
            });

            if (result != null && result['list'] != null) {
              model.contentList = result['list'];
              List nameList = [];
              for (var i = 0; i < model.contentList.length; i++) {
                MemberModel memberModel = model.contentList[i];
                nameList.add(memberModel.name);
              }
              model.showContent = nameList.join('、');
              controller.dataList.refresh();
              controller.getRequiredContentIsDone();
            }
          },
          child: backWidgetForChoose(
              model.title ?? '',
              model.showContent.isEmpty ? (model.prompt ?? '') : model.showContent,
              model.required!),
        );
      } else if (model.type == 9 || model.type == 18) {
        if (model.wordType == 0) {
          if (model.type == 18) {
            model.content = controller.userId;
          } else {
            model.content = jsonEncode([controller.userId]);
          }

          model.showContent = controller.name;
        }
        if (model.wordType == 1) {
          model.content = '无'; //无
          model.showContent = '未选择';
        }
        if (model.wordType == 2) {
          var content = controller.currentDeptDic['name'];
          model.content = content ?? '';

          model.showContent = content ?? '';

          if (model.type == 18) {
            var cont = controller.currentDeptDic['deptId'];
            model.content = cont ?? '';
          }
        }

        widget = InkWell(
          onTap: () async {},
          child: backWidgetForAuto(model),
        );
      } else if (model.type == 10 || model.type == 12) {
        widget = backWidgetForImage(model);
      } else if (model.type == 11) {
        String showContent = model.prompt!;
        if (model.addressChoose == 0 &&
            controller.postionInfo['address'] != null) {
          showContent = controller.postionInfo['address'];
          model.showContent = showContent;
          model.content = jsonEncode(controller.postionInfo.value);
        } else {
          if (model.addressChoose == 1) {
            showContent = model.showContent;
          }
        }
        widget = InkWell(
          onTap: () async {
            if (model.addressChoose == 1) {
              var result = await Channel().invokeMap(
                  Channel_Native_Approve_Postion,
                  model.content.isEmpty ? {} : jsonDecode(model.content));

              if(result == null) return;

              Map resultMap = result!;

              model.showContent = resultMap['address'];
              model.content = jsonEncode(resultMap);
              controller.dataList.refresh();
              controller.getRequiredContentIsDone();
              controller.dealCacheData();
            }
          },
          child:
              backWidgetForChoose(model.title ?? '', showContent, model.required ?? '0'),
        );
      } else if (model.type == 13) {
        widget = backMackUpclockWidget(model);
      } else if (model.type == 14) {
        widget = InkWell(
          onTap: () async {
            var result = await Get.toNamed('/choose-city');
            if (result != null && result['city'] != null) {
              model.showContent = result['city'];
              model.content = result['city'];
              controller.dataList.refresh();
              controller.getRequiredContentIsDone();
              controller.dealCacheData();
            }
          },
          child: backWidgetForChoose(
              model.title ?? '',
              model.showContent.isEmpty ? (model.prompt ?? '') : model.showContent,
              model.required!),
        );
      } else if (model.type == 19) {
        widget = backMackUpclockWidget(model);
      } else if (model.type == 20) {
        widget = backMackUpclockWidget(model);
      } else {
        widget = Container(
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 15, right: 15),
          height: 48,
          margin: EdgeInsets.only(top: 8),
          child: Text('无对应类型控件'),
        );
      }
      lists.add(widget);
    }
    return lists;
  }

  //9自动获取固定数据 /** 文字类型:wordType 0.姓名类；1.职位类；2.部门类 **/
  backWidgetForAuto(FormWidetModel model) {
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          height: 48,
          padding: EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  width: 110,
                  height: 48,
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.title,
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Expanded(
                  child: Container(
                alignment: Alignment.centerRight,
                child: Text(
                  model.showContent,
                  maxLines: 2,
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.desTextColor),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }

  //1短文本类型
  backWidgetForShortText(FormWidetModel model, {int index = 0}) {
    ChinaTextEditController msgController = ChinaTextEditController();
    FocusNode focusNode = FocusNode();
    if (model.type == 1) {
      model.msgController ??= ChinaTextEditController();
      model.msgController!.text = model.showContent;
      model.msgController!.value = TextEditingValue(
          text: model.showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: model.showContent.length)));
      msgController = model.msgController!;
      focusNode = model.focusNode;

      msgController.addListener(() {
        if (model.showContent != msgController.completeText) {
          model.showContent = msgController.completeText;
          model.content = msgController.completeText;
          controller.getRequiredContentIsDone(isTextField: true);
          controller.dealCacheData();
        }
      });
    } else if (model.type == 19) {
      String showContent = model.contentList[index];
      msgController = model.textECList[index];
      msgController.text = showContent;
      msgController.value = TextEditingValue(
          text: showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream, offset: showContent.length)));
      focusNode = model.focusNodeList[index];
      msgController.addListener(() {
        if (model.contentList[index] != msgController.completeText) {
          model.contentList[index] = msgController.completeText;
          controller.getRequiredContentIsDone(isTextField: true);
          controller.dealCacheData();
        }
      });
    }
    String suffix = model.suffix!;
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          padding: EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  Container(
                      width: 110,
                      height: 48,
                      alignment: Alignment.centerLeft,
                      child: RichText(
                          maxLines: 2,
                          text: TextSpan(
                              text: model.title,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                              children: [
                                TextSpan(
                                    text: model.required == '1' ? '*' : '',
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: ColorConfig.deleteCorlor))
                              ]))),
                  Offstage(
                    offstage: suffix.isEmpty,
                    child: Container(
                        width: 110,
                        height: 48,
                        alignment: Alignment.centerLeft,
                        child: RichText(
                            maxLines: 2,
                            text: TextSpan(
                              text: suffix,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.mainTextColor),
                            ))),
                  )
                ],
              ),
              Expanded(
                  child: Container(
                height: suffix.isEmpty ? 48 : 96,
                alignment: Alignment.centerRight,
                padding: EdgeInsets.only(top: suffix.isEmpty ? 0 : 48),
                child: TextField(
                  onSubmitted: (value) {},
                  onTap: () {},
                  focusNode: focusNode,
                  // onChanged: (value) {
                  //   //变化时给model赋值
                  //   if (model.type == 1) {
                  //     model.showContent = value;
                  //     model.content = value;
                  //   } else if (model.type == 19) {
                  //     model.contentList[index] = value;
                  //   }

                  //   controller.getRequiredContentIsDone(isTextField: true);
                  // },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(model.wordLimit)
                  ],
                  controller: msgController,
                  keyboardType: model.keyBoard == 2
                      ? TextInputType.numberWithOptions(decimal: true)
                      : TextInputType.text,
                  textInputAction: TextInputAction.unspecified,
                  textAlign: TextAlign.end,
                  // controller: controller.nameController,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 14,
                  ),
                  decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
                      border:
                          const OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: model.prompt,
                      hintStyle: const TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 14,
                      )),
                ),
              ))
            ],
          ),
        )
      ],
    );
  }

  //2长文本类型
  backWidgetForLongText(FormWidetModel model, {int index = 0}) {
    ChinaTextEditController msgController = ChinaTextEditController();
    FocusNode focusNode = FocusNode();
    if (model.type == 2) {
      model.msgController ??= ChinaTextEditController();
      model.msgController!.text = model.showContent;
      model.msgController!.value = TextEditingValue(
          text: model.showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: model.showContent.length)));
      msgController = model.msgController!;
      focusNode = model.focusNode;
      msgController.addListener(() {
        if (model.showContent != msgController.completeText) {
          model.showContent = msgController.completeText;
          model.content = msgController.completeText;
          controller.getRequiredContentIsDone(isTextField: true);
          controller.dealCacheData();
        }
      });
    } else if (model.type == 20) {
      String showContent = model.contentList[index];
      msgController = model.textECList[index];
      msgController.text = showContent;
      msgController.value = TextEditingValue(
          text: showContent,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream, offset: showContent.length)));
      focusNode = model.focusNodeList[index];
      msgController.addListener(() {
        if (model.contentList[index] != msgController.completeText) {
          model.contentList[index] = msgController.completeText;
          controller.getRequiredContentIsDone(isTextField: true);
          controller.dealCacheData();
        }
      });
    }
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          constraints: BoxConstraints(minHeight: 130),
          padding: EdgeInsets.only(left: 15, right: 15, bottom: 10),
          child: Column(
            children: [
              Container(
                  alignment: Alignment.centerLeft,
                  width: double.infinity,
                  height: 44,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.title,
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Container(
                child: TextField(
                  onSubmitted: (value) {},
                  // onChanged: (value) {
                  //   logger(
                  //       'msgcontrollerpo===${msgController.value.composing.isValid}==');
                  //   if (model.type == 2) {
                  //     model.showContent = value;
                  //     model.content = value;
                  //   } else if (model.type == 20) {
                  //     model.contentList[index] = value;
                  //   }
                  //   controller.getRequiredContentIsDone(isTextField: true);
                  // },
                  controller: msgController,
                  maxLines: null,
                  minLines: 1,
                  focusNode: focusNode,
                  textInputAction: TextInputAction.unspecified,
                  style: const TextStyle(
                    color: ColorConfig.mainTextColor,
                    fontSize: 14,
                  ),
                  decoration: InputDecoration(
                      isCollapsed: true,
                      contentPadding: EdgeInsets.only(top: 0, bottom: 0),
                      border: OutlineInputBorder(borderSide: BorderSide.none),
                      hintText: model.prompt,
                      hintStyle: TextStyle(
                        color: ColorConfig.desTextColor,
                        fontSize: 14,
                      )),
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  //4时间段类型 （1时间点2时间段）(0限时提交1正常提交)
  backWidgetForTime(FormWidetModel model) {
    if (model.type != 4) {
      if (model.nowChoose == 1) {
        model.content = DateTime.now().millisecondsSinceEpoch.toString();
        model.showContent = BaseInfo().formatTimestamp(
            DateTime.now().millisecondsSinceEpoch,
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      }
    }
    String rightStr = model.prompt!;
    String bottomRightStr = model.secPrompt!;
    if (model.content.isNotEmpty) {
      if (model.type == 3) {
        rightStr = model.showContent;
      }
    }
    if (model.type == 4) {
      if (model.longitudeStr.isNotEmpty) {
        rightStr = BaseInfo().formatTimestamp(
            int.parse(model.longitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      }
      if (model.latitudeStr.isNotEmpty) {
        bottomRightStr = BaseInfo().formatTimestamp(
            int.parse(model.latitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      }
    }
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Offstage(
                  offstage: model.isLimit == 0,
                  child: Column(
                    children: [
                      Container(
                        height: 48,
                        child: Container(
                          color: ColorConfig.whiteColor,
                          height: 48,
                          padding: EdgeInsets.only(left: 15, right: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                width: 110,
                                height: 48,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '限时提交',
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.mainTextColor),
                                ),
                              ),
                              Expanded(
                                  child: Container(
                                alignment: Alignment.centerRight,
                                child: Text(
                                  model.limitRemind!,
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: ColorConfig.desTextColor),
                                ),
                              ))
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        color: ColorConfig.backgroundColor,
                      )
                    ],
                  )),
              InkWell(
                onTap: () {
                  if (model.type == 3 && model.nowChoose != 1) {
                    DDDatePickerDialog(model.content.isEmpty
                            ? DateTime.now()
                            : DateTime.fromMillisecondsSinceEpoch(
                                int.parse(model.content)))
                        .show((time) {
                      model.content = time;
                      model.showContent = BaseInfo().formatTimestamp(
                          int.parse(model.content),
                          model.dateChoose == 0
                              ? 'yyyy-MM-dd HH:mm'
                              : model.dateChoose == 1
                                  ? 'yyyy-MM-dd'
                                  : 'HH:mm');
                      controller.dataList.refresh();
                      controller.getRequiredContentIsDone();
                    }, model.dateChoose!);
                  }
                  if (model.type == 4) {
                    DDDatePickerDialog(model.longitudeStr.isEmpty
                            ? DateTime.now()
                            : DateTime.fromMillisecondsSinceEpoch(
                                int.parse(model.longitudeStr)))
                        .show((time) {
                      model.longitudeStr = time;
                      controller.dataList.refresh();
                    }, model.dateChoose!);
                  }
                },
                child: backWidgetForChoose(
                    model.title??'', rightStr, model.required ?? '0',isHiddenArrow: model.type==3&&model.nowChoose==1),
              ),
              Offstage(
                offstage: model.type == 3,
                child: Column(
                  children: [
                    Divider(
                      height: 1,
                      color: ColorConfig.backgroundColor,
                    ),
                    InkWell(
                      onTap: () {
                        if (model.type == 4) {
                          DDDatePickerDialog(model.latitudeStr.isEmpty
                                  ? DateTime.now()
                                  : DateTime.fromMillisecondsSinceEpoch(
                                      int.parse(model.latitudeStr)))
                              .show((time) {
                            model.latitudeStr = time;
                            controller.dataList.refresh();
                            controller.getRequiredContentIsDone();
                 
                          }, model.dateChoose!);
                        }
                      },
                      child: backWidgetForChoose(
                          model.secTitle ?? '', bottomRightStr, model.required ?? '0'),
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  //10图片类型
  backWidgetForImage(FormWidetModel model) {
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          width: double.infinity,
          color: ColorConfig.whiteColor,
          padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                height: 48,
                alignment: Alignment.centerLeft,
                child: RichText(
                    maxLines: 2,
                    text: TextSpan(
                        text: model.title,
                        style: TextStyle(
                            fontSize: 14, color: ColorConfig.mainTextColor),
                        children: [
                          TextSpan(
                              text: model.required == '1' ? '*' : '',
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorConfig.deleteCorlor))
                        ])),
              ),
              Column(
                children: backImageData(model),
              )
            ],
          ),
        )
      ],
    );
  }

  backImageData(FormWidetModel model) {
    List<Widget> lists = [];

    if (model.fileList.isNotEmpty) {
      for (var i = 0; i < model.fileList.length; i++) {
        Map fileMap = model.fileList[i];
        String fileName = fileMap['fileName'];
        String path = fileMap['filePath'];
        String iconStr = BaseInfo().panFileType(fileMap['fileName']);
        lists.add(Column(
          children: [
            InkWell(
                onTap: () {
                  List fileList = [];
                  for (var j = 0; j < model.fileList.length; j++) {
                    Map fileMap = model.fileList[j];

                    fileList.add(fileMap['filePath']);
                  }

                  if (model.type == 10) {
                    Get.toNamed(Routes.COMMON_SCROLL_PIC,
                        arguments: {'imageUrls': fileList, 'currentIndex': i});
                  }
                  if (model.type == 12) {
                    CosHelper.openFile(path);
                  }
                },
                child: Container(
                  width: double.infinity,
                  height: 46,
                  padding: EdgeInsets.fromLTRB(12, 0, 12, 0),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: ColorConfig.backgroundColor),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ImageLoader(
                        url: model.type == 10 ? path : iconStr,
                        width: 32,
                        height: 32,
                      ),

                      SizedBox(
                        width: 8,
                      ),
                      Expanded(
                          child: Container(
                        child: Text(
                          fileName,
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                        ),
                      )),
                      InkWell(
                        onTap: () {
                          model.fileList.removeAt(i);
                          model.contentList.removeAt(i);
                          controller.dataList.refresh();
                        },
                        child: Container(
                          width: 20,
                          height: 20,
                          child: Image.asset(
                              'assets/images/3.0x/approve_delete.png'),
                        ),
                      )
                    ],
                  ),
                )),
            SizedBox(
              height: 8,
            )
          ],
        ));
      }
    }

    Widget upWidget = InkWell(
      onTap: () {
        if (model.type == 10) {
          SettingWidget().showCupertinoActionSheetForPage(
              Get.context!, ['拍照', '从相册选取'], (value) {
            if (value == 0) {
              controller.tackPhotoWithCamera(model);
            } else {
              controller.pickPhonto(model);
            }
          });
        } else {
          controller.pickFile(model);
        }
      },
      child: Container(
        width: double.infinity,
        height: 46,
        padding: EdgeInsets.only(bottom: 16),
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: Image.asset('assets/images/3.0x/approve_upLoad.png'),
              ),
              Container(
                child: Text(
                  model.type == 10 ? ' 上传图片' : ' 上传附件',
                  style:
                      TextStyle(fontSize: 14, color: ColorConfig.themeCorlor),
                ),
              )
            ],
          ),
        ),
      ),
    );
    lists.add(upWidget);
    return lists;
  }

  //13补卡样式控件
  backMackUpclockWidget(FormWidetModel model) {
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Column(
          children: backMakeupTime(model),
        ),
        model.contentList.length == model.maxNum &&
                model.type != 19 &&
                model.type != 20
            ? Container(
                alignment: Alignment.center,
                height: 40,
                child: Text(
                  '最多可提交 ${model.maxNum} 个补卡时间',
                  style:
                      TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
                ),
              )
            : InkWell(
                onTap: () {
                  model.contentList.add('');
                  if (model.type == 19 || model.type == 20) {
                    model.textECList.add(ChinaTextEditController());
                    model.focusNodeList.add(FocusNode());
                  }
                  controller.dataList.refresh();
                },
                child: Container(
                  height: 40,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                      ),
                      Container(
                        child: Text(
                          model.type == 13
                              ? ' 添加补卡'
                              : model.type == 19
                                  ? ' 添加短文本'
                                  : model.type == 20
                                      ? ' 添加长文本'
                                      : '',
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.themeCorlor),
                        ),
                      )
                    ],
                  ),
                ),
              )
      ],
    );
  }

  backMakeupTime(FormWidetModel model) {
    List<Widget> lists = [];
    for (var i = 0; i < model.contentList.length; i++) {
      lists.add(Column(
        children: [
          Container(
            width: double.infinity,
            height: 50,
            padding: EdgeInsets.only(left: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: model.type == 13
                              ? '时间 ${i + 1}'
                              : model.type == 19
                                  ? '短文本 ${i + 1}'
                                  : model.type == 20
                                      ? '长文本 ${i + 1}'
                                      : '',
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: model.required == '1' ? '*' : '',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ])),
                ),
                Offstage(
                  offstage: model.contentList.length == 1,
                  child: InkWell(
                    onTap: () {
                      String currentStr = model.contentList[i];
                      model.contentList.removeAt(i);
                      if (model.type == 19 || model.type == 20) {
                        model.textECList.removeAt(i);
                        model.focusNodeList.removeAt(i);
                      }
                      controller.dataList.refresh();
                      if (model.required == '1' &&
                          currentStr.isEmpty &&
                          !controller.isRequestProcess) {
                        controller.getRequiredContentIsDone();
                      }
                      controller.dealCacheData();
                    },
                    child: Container(
                      width: 50,
                      height: 50,
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: 18,
                        height: 18,
                        child: Image.asset(
                            'assets/images/3.0x/approve_delete.png'),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          InkWell(
            onTap: () {
              if (model.type == 13) {
                String timeSatmp = model.contentList[i];
                DDDatePickerDialog(timeSatmp.isEmpty
                        ? DateTime.now()
                        : DateTime.fromMillisecondsSinceEpoch(
                            int.parse(timeSatmp)))
                    .show((time) {
                  model.contentList[i] = time;
                  controller.dataList.refresh();
                  controller.getRequiredContentIsDone();
                }, model.dateChoose!);
              }
            },
            child: model.type == 13
                ? backWidgetForChoose(
                    model.title ?? '',
                    model.contentList[i] == ''
                        ? '请选择'
                        : BaseInfo().formatTimestamp(
                            int.parse(model.contentList[i]),
                            model.dateChoose == 0
                                ? 'yyyy-MM-dd HH:mm'
                                : model.dateChoose == 1
                                    ? 'yyyy-MM-dd'
                                    : 'HH:mm'),
                    model.required ?? '0')
                : model.type == 19
                    ? backWidgetForShortText(model, index: i)
                    : model.type == 20
                        ? backWidgetForLongText(model, index: i)
                        : Container(),
          ),
        ],
      ));
    }
    return lists;
  }

  backWidgetForChoose(String leftStr, String rightStr, String required,{bool isHiddenArrow = false}) {
    return Column(
      children: [
        SizedBox(
          height: 1,
        ),
        Container(
          color: ColorConfig.whiteColor,
          height: 48,
          padding: EdgeInsets.only(left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                  width: 110,
                  height: 48,
                  alignment: Alignment.centerLeft,
                  child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                          text: leftStr,
                          style: TextStyle(
                              fontSize: 14, color: ColorConfig.mainTextColor),
                          children: [
                            TextSpan(
                                text: required == '1' ? '*' : '',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: ColorConfig.deleteCorlor))
                          ]))),
              Expanded(
                  child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                      child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      rightStr,
                      maxLines: 2,
                      style: TextStyle(
                          fontSize: 14, color: ColorConfig.desTextColor),
                    ),
                  )),
                  Container(
                    child: isHiddenArrow?Container():Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 5,
                        ),
                        SizedBox(
                          width: 9,
                          height: 17,
                          child: Image.asset('assets/images/3.0x/mine_right.png'),
                        )
                      ],
                    ),
                  )
                ],
              ))
            ],
          ),
        ),
      ],
    );
  }
}
