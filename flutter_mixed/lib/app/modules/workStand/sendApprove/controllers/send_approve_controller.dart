import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/channel/channel.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/cos/pan_datasource.dart';
import 'package:flutter_mixed/app/common/dialog/msg-dialog.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/modules/contact/model/org/dept_members_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/appprove_corg_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/filemodel.dart';
import 'package:flutter_mixed/app/utils/storage.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../../common/api/LoginApi.dart';
import '../../../../common/base_info/info.dart';
import '../../../../common/widgets/widgets.dart';
import '../../../../permission/permission_util.dart';
import '../../../../retrofit/datasource/workbench_datasource.dart';
import '../../../../utils/http.dart';
import '../../../contact/model/org/org_model.dart';
import '../../models/form_model.dart';
import '../../models/procee_model.dart';

class SendApproveController extends GetxController {
  String userId = '';
  String title = ''; //模版名称

  String orgId = '';
  String modelId = '';
  RxList dataList = [].obs; //发起人表单数组
  List sendModelName = []; //发起人表单名称
  RxList mainList = [].obs; //主表单数据
  String name = '';
  RxList deptList = [].obs;
  Map currentDeptDic = {};
  Map dataDic = {}; //模版原数据
  RxList processList = [].obs; //流程数据
  RxList processModelFormList = [].obs; //办理表单数组
  List processModelName = []; //办理表单名称
  bool isAllowExternalAssignee = false; //是否允许选择外部联系人
  String approveId = '';

  bool isRequestProcess = false; //流程获取是否正常返回

  List upModelArray = [];
  String uploadBucket = '';

  OrgModel? orgModel;
  RxMap postionInfo = {}.obs;

  int sendType = 0; //0正常发送1重新发送
  List contentList = []; //详情里待重新提交的数据

  int version = 0; //当前模版版本号

  int isAllowWithdraw = 1; //是否允许撤回 0.不允许 1.允许

  RxList corgIdList = [].obs;
  AppproveCorgModel? currentCorgModel;
  @override
  void onInit() async {
    super.onInit();
    orgModel = Get.arguments['model'];
    orgId = Get.arguments['orgId'];
    modelId = Get.arguments['modelId'];
    if (Get.arguments['sendType'] != null) {
      sendType = Get.arguments['sendType'];
    }
    if (Get.arguments['contentList'] != null) {
      contentList = Get.arguments['contentList'];
    }
    if (Get.arguments['corgId'] != null) {
      currentCorgModel = AppproveCorgModel(corgId: Get.arguments['corgId'],orgName: '');
    }
    if (Get.arguments['approveId'] != null) {
      approveId = Get.arguments['approveId'];
    }
    Map userInfo = await UserDefault.getData(Define.TOKENKEY);
    name = userInfo['name'];
    userId = userInfo['userId'];
  }

  // if(MediaQuery.of(context).viewInsets.bottom==0){
  //             //关闭键盘
  //            }else{
  //             //显示键盘
  //           }

  @override
  void onReady() {
    super.onReady();
    getCorgIds();
    getDepts();
    getModelForm();
    //_getCompanyCapacity();
  }

  @override
  void onClose() {
    super.onClose();
  }

  getCorgIds() async{
    try {
      var wrokDatasource = WorkbenchDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await wrokDatasource.getCorgIds(orgId);
      if (resp.success()) {
        List<AppproveCorgModel?>? corgIds = resp.data;
        if (corgIds != null) {
          corgIdList.value = corgIds;
          if (corgIds.isNotEmpty) {
            if (sendType == 0) {
              currentCorgModel = corgIds.first;
            }else if(sendType == 1){
              for (var i = 0; i < corgIds.length; i++) {
                AppproveCorgModel? corgModel = corgIds[i];
                if (corgModel?.corgId == currentCorgModel?.corgId) {
                  currentCorgModel = corgModel;
                  break;
                }
              }
            }
            
          }
        }
      }
    } catch (e) {
      
    }
  }

  //获取所在所有部门
  getDepts() {
    DioUtil().get('${ORGApi.GETDEPTS}/$orgId', null, true, () {},isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        deptList.value = data['data'];
        if (deptList.isNotEmpty) {
          currentDeptDic = deptList[0];
        }
        dataList.refresh();
      } else {
        toast('${data['msg']}');
      }
    });
  }

  List<FocusNode> focusNodeList = [];

  //获取模版列表
  getModelForm() async {
    DioUtil()
        .get('${ApproveApi.APPROVEMODFORM}/$modelId', null, true, () {},isShowLoading: false)
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        focusNodeList.clear();

        bool isHavePositonAuto = false; //是否存在自动获取定位功能
        dataList.clear();
        dataDic = data['data'];
        List modelFormList = dataDic['modelFormList'];
        version = dataDic['version'];
        isAllowWithdraw = dataDic['allowWithdraw'] ?? 1;
        dynamic cacheMap;
        if (sendType == 0) {
          dynamic cacheDataMap =
              await UserDefault.getData(Define.APPROVE_SEND_CACHEKEY);
          if (cacheDataMap != null) {
            String signKey = cacheDataMap['sign'];
            int cacheVersion = cacheDataMap['version'];
            if (signKey == '$orgId-$modelId' && version == cacheVersion) {
              cacheMap = cacheDataMap;
            } else {
              await UserDefault.removeData(Define.APPROVE_SEND_CACHEKEY);
            }
          }
        } else {
          await UserDefault.removeData(Define.APPROVE_SEND_CACHEKEY);
        }
        List nameList = [];
        for (var i = 0; i < modelFormList.length; i++) {
          Map widgetInfo = modelFormList[i];
          processModelName.add(widgetInfo);
          List widgetList = widgetInfo['widgetList'];
          nameList.add(widgetInfo['modelFormName']);

          List modelList = [];
          for (var j = 0; j < widgetList.length; j++) {
            Map<String, dynamic> widgetMap = widgetList[j];
            FormWidetModel widgetModel = FormWidetModel.fromJson(widgetMap);
            if (cacheMap != null) {
              List cacheDataList = cacheMap['data'];
              List cacheMapList = cacheDataList[i];
              Map<String, dynamic> cacheWidgetMap = cacheMapList[j];
              widgetModel = FormWidetModel.fromJson(cacheWidgetMap, isCache: 1);
            }

            if (widgetModel.type == 11 && widgetModel.addressChoose == 0) {
              isHavePositonAuto = true;
            }
            if (widgetModel.type == 1 || widgetModel.type == 2) {
              focusNodeList.add(widgetModel.focusNode);
            }
            dealResubmitData(widgetModel);
            modelList.add(widgetModel);
          }
          if (widgetInfo['isMaster'] == 1) {
            mainList.value = modelList;
          }
          dataList.add(modelList);
        }
        sendModelName = nameList;

        dataList.refresh();
        if (isHavePositonAuto) {
          var result =
              await Channel().invokeMap(Channel_Native_Approve_PostionAuto);
          postionInfo.value = result!;
          dataList.refresh();
        }

        //模版无必填或必填为自动获取
        Future.delayed(Duration(milliseconds: 300)).then((value) {
          getRequiredContentIsDone();
        });
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //处理待重新提交的数据
  dealResubmitData(FormWidetModel widgetModel) {
    if (sendType == 1) {
      //重新提交数据赋值
      for (var element in contentList) {
        String elementWidgetId = element['widgetId'];
        String elementContent = element['content'];
        if (elementWidgetId == widgetModel.widgetId) {
          widgetModel.content = elementContent;
          widgetModel.showContent = elementContent;
          if (widgetModel.type == 3 && widgetModel.content.isNotEmpty) {
            widgetModel.showContent = BaseInfo().formatTimestamp(
                int.parse(widgetModel.content),
                widgetModel.dateChoose == 0
                    ? 'yyyy-MM-dd HH:mm'
                    : widgetModel.dateChoose == 1
                        ? 'yyyy-MM-dd'
                        : 'HH:mm');
          }
          if (widgetModel.type == 4 && widgetModel.content.isNotEmpty) {
            dynamic contentJson = jsonDecode(widgetModel.content);
            dynamic startTime = contentJson['startTime'];
            dynamic endTime = contentJson['endTime'];
            if (startTime != null) {
              if (startTime != '' && startTime != 0) {
                String timeStr = '';
                if (startTime is String) {
                  timeStr = startTime;
                } else {
                  timeStr = startTime.toString();
                }
                widgetModel.longitudeStr = timeStr;
              }
            }
            if (endTime != null) {
              if (endTime != '' && endTime != 0) {
                String timeStr = '';
                if (endTime is String) {
                  timeStr = endTime;
                } else {
                  timeStr = endTime.toString();
                }
                widgetModel.latitudeStr = timeStr;
              }
            }
          }

          if (widgetModel.type == 8 && widgetModel.content.isNotEmpty) {
            dynamic resendContentList = jsonDecode(widgetModel.content);
            List memeList = [];
            List nameList = [];
            for (Map<String, dynamic> memMap in resendContentList) {
              MemberModel memberModel = MemberModel.fromJson(memMap);
              memeList.add(memberModel);
              nameList.add(memberModel.name);
            }

            widgetModel.showContent = nameList.join('、');
            widgetModel.contentList = memeList;
          }
          if (widgetModel.type == 10 || widgetModel.type == 12) {
            //图片类和文件类置空重新选择
            widgetModel.content = '';
            widgetModel.contentList = [];
          }
          if (widgetModel.type == 11 && widgetModel.content.isNotEmpty) {
            if (widgetModel.addressChoose != 0) {
              dynamic contentJson = jsonDecode(widgetModel.content);
              widgetModel.showContent = contentJson['address'];
            }
          }
          if (widgetModel.type == 13 ||
              widgetModel.type == 19 ||
              widgetModel.type == 20 && widgetModel.content.isNotEmpty) {
            dynamic resendContentList = jsonDecode(widgetModel.content);
            widgetModel.contentList = resendContentList;

            if (widgetModel.type == 19 || widgetModel.type == 20) {
              List<ChinaTextEditController> resendMsgControllerList = [];
              List<FocusNode> resendFocusNodeList = [];
              for (var i = 0; i < resendContentList.length; i++) {
                ChinaTextEditController msgController =
                    ChinaTextEditController();
                msgController.text = resendContentList[i];
                FocusNode node = FocusNode();
                resendMsgControllerList.add(msgController);
                resendFocusNodeList.add(node);
              }
              widgetModel.textECList = resendMsgControllerList;
              widgetModel.focusNodeList = resendFocusNodeList;
            }
          }
          if (widgetModel.type == 15 && widgetModel.content.isNotEmpty) {
            DateTime time = DateTime.fromMillisecondsSinceEpoch(
                int.parse(widgetModel.content));
            DateTime zeroTime = DateTime.fromMillisecondsSinceEpoch(0);
            var differenceDay = time.difference(zeroTime);

            DateTime hourTime = DateTime.fromMillisecondsSinceEpoch(
                int.parse(widgetModel.content) -
                    differenceDay.inDays * 24 * 60 * 60 * 1000);
            var differenceHour = hourTime.difference(zeroTime);

            DateTime minuteTime = DateTime.fromMillisecondsSinceEpoch(
                int.parse(widgetModel.content) -
                    differenceDay.inDays * 24 * 60 * 60 * 1000 -
                    differenceHour.inHours * 60 * 60 * 1000);
            var differenceMinute = minuteTime.difference(zeroTime);

            if (widgetModel.dateChoose == 0) {
              widgetModel.contentList = [
                differenceDay.inDays,
                differenceHour.inHours,
                differenceMinute.inMinutes
              ];
            }
            if (widgetModel.dateChoose == 1) {
              widgetModel.contentList = [differenceDay.inDays];
            }
            if (widgetModel.dateChoose == 2) {
              widgetModel.contentList = [
                differenceHour.inHours,
                differenceMinute.inMinutes
              ];
            }
            widgetModel.showContent =
                '${differenceDay.inDays}天${differenceHour.inHours}小时${differenceMinute.inMinutes}分钟';
          }

          if (widgetModel.type == 16 && widgetModel.content.isNotEmpty) {
            if (widgetModel.chooseFrameList != null) {
              for (var frameMap in widgetModel.chooseFrameList!) {
                if (frameMap['frameId'] == widgetModel.content) {
                  widgetModel.showContent = frameMap['title'];
                }
              }
            }
          }
          if (widgetModel.type == 17 && widgetModel.content.isNotEmpty) {
            List idList = jsonDecode(widgetModel.content);
            List resultList = [];
            for (var frameMap in widgetModel.chooseFrameList!) {
              for (var frameId17 in idList) {
                if (frameMap['frameId'] == frameId17) {
                  resultList.add(frameMap);
                }
              }
            }
            widgetModel.contentList = resultList;
            List titleList = [];
            for (var i = 0; i < widgetModel.contentList.length; i++) {
              Map contentMap = widgetModel.contentList[i];
              titleList.add(contentMap['title']);
            }
            widgetModel.showContent = titleList.join('、');
          }
        }
      }
    }
  }

  //判断必填内容是否填写完成
  getRequiredContentIsDone({bool isTextField = false}) {
    //是否是文本判定 文本判定不影响流程获取
    bool isDone = true;

    for (var i = 0; i < mainList.length; i++) {
      FormWidetModel model = mainList[i];

      if (model.required == '1') {
        Map modelMap = model.toJson();
        if (modelMap['isDone'] == 0) {
          isDone = false;
        }
      }
    }
    if (isDone) {
      if (!(isTextField && isRequestProcess)) {
        getApprove();
      }
    }
  }

  //获取审批流程表单
  getApprove() async {
    isRequestProcess = false;
    List getApproveProcessList = [];
    for (var i = 0; i < mainList.length; i++) {
      FormWidetModel model = mainList[i];
      getApproveProcessList
          .add({'widgetId': model.widgetId, 'value': model.content});
    }

    Map param = {
      'modelId': dataDic['modelId'],
      'version': dataDic['version'],
      'orgId': orgId,
      'corgId':currentCorgModel?.corgId ?? orgId,
      'deptId': currentDeptDic['deptId'],
      'widgetValueList': getApproveProcessList
    };

    DioUtil().post(ApproveApi.APPROVEPROCESS, param, true, () {
      isRequestProcess = false;
    }, isShowLoading: false).then((data) {
      if (data == null) return;
      if (data!['code'] == 1) {
        isRequestProcess = true;
        Map dataMap = data['data'];
        List initiateProcessNodeList = dataMap['initiateProcessNodeList'];
        if (dataMap['externalAssignee'] == 1) {
          isAllowExternalAssignee = true;
        } else {
          isAllowExternalAssignee = false;
        }
        processList.value = initiateProcessNodeList
            .map((e) => ProcessModel.fromJson(e))
            .toList();

        List modelFormList = dataMap['modelFormList'];

        List tempModelList = [];
        List nameList = [];
        for (var i = 0; i < modelFormList.length; i++) {
          Map widgetInfo = modelFormList[i];
          nameList.add(widgetInfo['modelFormName']);
          List widgetList = widgetInfo['widgetList'];

          List modelList =
              widgetList.map((item) => FormWidetModel.fromJson(item)).toList();

          tempModelList.add(modelList);
        }
        processModelFormList.value = tempModelList;
        processModelName = nameList;
        dataList.refresh();
      } else {
        isRequestProcess = false;
        toast('${data['msg']}');
      }
    });
  }

  jugeDataIsDone() {
    bool isDone = true;
    bool isWrong = false; //时间段类结束时间是否小于开始时间
    String errorMsg = ''; //错误提示
    for (var i = 0; i < dataList.length; i++) {
      List modelList = dataList[i];
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        if (model.required == '1') {
          Map modelMap = model.toJson();

          if (modelMap['isDone'] == 0) {
            isDone = false;
          }
        }
        if (model.type == 4) {
          if (model.longitudeStr.isNotEmpty && model.latitudeStr.isNotEmpty) {
            if (int.parse(model.latitudeStr) <= int.parse(model.longitudeStr)) {
              isWrong = true;
              errorMsg = '${model.secTitle}需大于${model.title}';
            }
          }
        }
      }
    }
    if (!isDone) {
      toast('有必填项未选择');
      return;
    }
    if (isWrong) {
      toast(errorMsg);
      return;
    }
    if (!isRequestProcess) {
      toast('流程未获取');
      return;
    }
    verifyApproveLimit();
  }

  //校验审批限时
  verifyApproveLimit() {
    Map param = {};
    List modelFormList = dataDic['modelFormList']; //所有表单model数据
    for (var i = 0; i < modelFormList.length; i++) {
      Map modelInfo = modelFormList[i];

      List modelList = dataList[i];
      List widgetContentList = [];
      FormWidetModel tempModel = FormWidetModel();
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        if (model.isLimit! > 0) {
          List timeArray = [];
          param['dayNumber'] = model.dayNumber;
          param['limitType'] = model.isLimit;
          if (model.type == 3) {
            timeArray.add(model.content);
          }
          if (model.type == 4) {
            timeArray.add(model.longitudeStr);
            timeArray.add(model.latitudeStr);
          }
          if (model.type == 13) {
            timeArray.addAll(model.contentList);
          }
          param['time'] = timeArray;
          break;
        }
      }
    }
    if (param['time'] == null) {
      sendApprove();
    } else {
      DioUtil()
          .post(ApproveApi.APPROVEVERIFYLIMIT, param, true, () {})
          .then((data) {
        if (data == null) return;
        if (data!['code'] == 1) {
          sendApprove();
        } else {
          toast('${data['msg']}');
        }
      });
    }
  }

  //发布审批
  sendApprove() {
    if (currentDeptDic['deptId'] == null) {
      toast('未获取到部门信息');
      return;
    }
    List modelFormContentList = []; //需传的表单数据参数
    List modelFormList = dataDic['modelFormList']; //所有表单model数据
    List widgetValueList = []; //主表单数据参数

    List listShow = [];
    for (var i = 0; i < modelFormList.length; i++) {
      Map modelInfo = modelFormList[i];

      List modelList = dataList[i];
      List widgetContentList = [];
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        Map jsonMap = model.toJson();
        jsonMap.remove('isDone');
        widgetContentList.add(jsonMap);
        Map widgetValueMap = {};
        if (modelInfo['isMaster'] == 1) {
          //主表单标识
          widgetValueMap['widgetId'] = model.widgetId;
          widgetValueMap['value'] = model.toJson()['content'];

          widgetValueList.add(widgetValueMap);

          List backList = backContentWithModel(model);
          for (var k = 0; k < backList.length; k++) {
            if (listShow.length < 3) {
              listShow.add(backList[k]);
            }
          }
        }
      }

      Map contentMap = {
        'modelFormName': modelInfo['modelFormName'],
        'widgetContentList': widgetContentList
      };
      modelFormContentList.add(contentMap);
    }

    List submitterAssigneeList = [];
    for (var i = 0; i < processList.length; i++) {
      Map submitMap = {};
      ProcessModel model = processList[i];
      if (model.isSubmitterChoose == 1) {
        if (model.assigneeList.isEmpty) {
          if (model.handleType == 1) {
            toast('请选择审批人');
            return;
          }
          if (model.handleType == 2) {
            toast('请选择办理人');
            return;
          }
        } else {
          List assigneeList = [];
          for (var j = 0; j < model.assigneeList.length; j++) {
            Map userDic = model.assigneeList[j];
            assigneeList.add(userDic['userId']);
          }
          submitMap['assigneeList'] = assigneeList;
          submitMap['processNodeId'] = model.processNodeId;
        }
      }
      if (submitMap['processNodeId'] != null) {
        submitterAssigneeList.add(submitMap);
      }
    }

    Map param = {
      'approveId': approveId,
      'deptId': currentDeptDic['deptId'],
      'modelFormContentList': modelFormContentList,
      'modelId': dataDic['modelId'],
      'orgId': orgId,
      'corgId': currentCorgModel?.corgId ?? orgId,
      'submitterAssigneeList': submitterAssigneeList,
      'oneApproveContent': listShow.isNotEmpty ? listShow[0] : '',
      'twoApproveContent': listShow.length > 1 ? listShow[1] : '',
      'threeApproveContent': listShow.length > 2 ? listShow[2] : '',
      'version': dataDic['version'],
      'widgetValueList': widgetValueList
    };
    if (isAllowWithdraw == 0) {
      MsgDiaLog('提示', '管理员设置该审批提交后不可撤回，是否继续提交', '取消', '确定', () {
        Navigator.of(Get.context!).pop();
      }, () {
        Navigator.of(Get.context!).pop();
        _requestCreate(param);
      }).show();
      return;
    } else {
      _requestCreate(param);
    }
  }

  _requestCreate(param) {
    logger('=======param===$param=====');
    DioUtil()
        .post(ApproveApi.APPROVECREATE, param, true, () {})
        .then((data) async {
      if (data == null) return;
      if (data['code'] == 1) {
        await UserDefault.removeData(Define.APPROVE_SEND_CACHEKEY);
        toast('创建成功');
        Get.back(result: data);
        eventBus.fire({'refreshApproveListPage': 4});
        Channel().invoke(Channel_sendIM_synchronous, {});
      } else {
        toast('${data['msg']}');
      }
    });
  }

  //根据model返回approveContent
  backContentWithModel(FormWidetModel model) {
    String contentStr =
        '${model.title}:${model.showContent.isEmpty ? model.type == 1 || model.type == 2 ? '未填写' : '未选择' : model.showContent}';

    List approveContentList = [];

    if (model.type == 10) {
      contentStr =
          '${model.title}:${model.contentList.isEmpty ? '未上传' : '${model.contentList.length}张图片'}';
    }
    if (model.type == 12) {
      contentStr =
          '${model.title}:${model.contentList.isEmpty ? '未上传' : '${model.contentList.length}个附件'}';
    }
    if (model.type == 13) {
      List timeList = [];

      List tempList = jsonDecode(model.content);
      for (var i = 0; i < tempList.length; i++) {
        String timeStr = tempList[i];
        if (timeStr.isNotEmpty) {
          timeList.add(BaseInfo()
              .formatTimestamp(int.parse(timeStr), 'yyyy-MM-dd HH:mm'));
        } else {
          timeList.add('未选择');
        }
      }
      contentStr =
          '${model.title}:${timeList.isEmpty ? '未选择' : timeList.join(',')}';
    }
    approveContentList = [contentStr];
    if (model.type == 4) {
      approveContentList.clear();
      String title = '';
      String timeStr = '';
      title = model.title!;
      if (model.longitudeStr.isNotEmpty) {
        timeStr = BaseInfo().formatTimestamp(
            int.parse(model.longitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      } else {
        timeStr = '未选择';
      }
      approveContentList.add('$title:$timeStr');

      title = model.secTitle!;
      if (model.latitudeStr.isNotEmpty) {
        timeStr = BaseInfo().formatTimestamp(
            int.parse(model.latitudeStr),
            model.dateChoose == 0
                ? 'yyyy-MM-dd HH:mm'
                : model.dateChoose == 1
                    ? 'yyyy-MM-dd'
                    : 'HH:mm');
      } else {
        timeStr = '未选择';
      }
      approveContentList.add('$title:$timeStr');
    }
    if (model.type == 19 || model.type == 20) {
      approveContentList.clear();

      List tempList = jsonDecode(model.content);
      for (var i = 0; i < tempList.length; i++) {
        String showStr = tempList[i];
        approveContentList
            .add('${model.title}:${showStr.isEmpty ? '未填写' : showStr}');
      }
    }

    return approveContentList;
  }

  tackPhotoWithCamera(FormWidetModel model) async {
    var r = await PermissionUtil.checkCameraPermission(Get.context!,
        tip: cameraPhotoPermissionTip);
    if (!r) return;

    List modelFormList = dataDic['modelFormList'];
    int picCounts = 0;
    for (var i = 0; i < modelFormList.length; i++) {
      List modelList = dataList[i];
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        if (model.type == 10) {
          picCounts += model.contentList.length;
        }
      }
    }

    if (picCounts >= 20) {
      toast('最多上传20张图片');
      return;
    }

    File? file;
    if (Platform.isIOS) {
      //iOS使用插件会出现相机功能一直启用，右上角绿点一直展示的问题
      var result = await Channel()
          .invokeMap(Channel_call_iOS_camera, {'allowsEditing': 0});

      String filePath = result!['filePath'];
      file = File(filePath);
    } else if (Platform.isAndroid) {
      final AssetEntity? entity =
          await CameraPicker.pickFromCamera(Get.context!);

      file = await entity!.file;
    }

    if (file == null) return;

    FileModel fileModel = FileModel();
    fileModel.hash = '';
    fileModel.fileId = '';
    List fileNameList = file.path.split('/');
    fileModel.fileName = fileNameList.last;
    fileModel.fileType = 0;
    fileModel.filePath = file.path;
    fileModel.file = file;

    await startDealFile(fileModel);
    dataList.refresh();
    getFileIds(model);
  }

  //选择图片
  pickPhonto(FormWidetModel model) async {
    var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return;

    //最多上传20张图片
    List modelFormList = dataDic['modelFormList'];
    int picCounts = 0;
    for (var i = 0; i < modelFormList.length; i++) {
      List modelList = dataList[i];
      for (var j = 0; j < modelList.length; j++) {
        FormWidetModel model = modelList[j];
        if (model.type == 10) {
          picCounts += model.contentList.length;
        }
      }
    }

    if (picCounts >= 20) {
      toast('最多上传20张图片');
      return;
    }

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: AssetPickerConfig(
            maxAssets: 20 - picCounts > 9 ? 9 : 20 - picCounts,
            requestType: RequestType.image));
    if (assets == null) return;
    for (var i = 0; i < assets.length; i++) {
      AssetEntity entity = assets[i];
      File? file = await entity.file;

      FileModel fileModel = FileModel();
      fileModel.hash = '';
      fileModel.fileId = '';
      List fileNameList = file!.path.split('/');
      fileModel.fileName = fileNameList.last;
      fileModel.fileType = 0;
      fileModel.filePath = file.path;
      fileModel.file = file;

      await startDealFile(fileModel);
    }
    dataList.refresh();
    getFileIds(model);
  }

  //选择文件
  pickFile(FormWidetModel model) async {
    FilePickerResult? result;
    try {
      result = await FilePicker.platform.pickFiles();
    } catch (e) {
      toast('请在担当权限设置中开启：允许使用文件和文档权限');
    }
    if (result != null) {
      File file = File(result.files.single.path!);
      FileModel fileModel = FileModel();
      fileModel.hash = '';
      fileModel.fileId = '';
      List fileNameList = file.path.split('/');
      fileModel.fileName = fileNameList.last;
      fileModel.fileType = 1;
      fileModel.filePath = file.path;
      fileModel.file = file;

      await startDealFile(fileModel);
    } else {
      // User canceled the picker
    }
    dataList.refresh();
    getFileIds(model);
  }

  Future startDealFile(FileModel fileModel) async {
    var completer = Completer<String>();
    await saveFile(fileModel).then((value) {
      fileModel.savePath = value;
      fileModel.hash = BaseInfo.generateMD5(value);
      upModelArray.add(fileModel);
      if (completer.isCompleted == false) {
        completer.complete('');
      }
    });
    return completer.future;
  }

  //保存图片
  Future<String> saveFile(FileModel fileModel) async {
    File file = fileModel.file!;
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;

    String startPath = '$docment/approve/images';
    if (fileModel.fileType == 1) {
      startPath = '$docment/approve/files';
    }
    String filePath = '$startPath/$fileName';

    Directory directory = Directory(startPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  //获取fileId
  getFileIds(FormWidetModel model) async{
    try {
      var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await panDatasource.getPanFields(upModelArray.length);
      if (resp.success()) {
        List? idList = resp.data;
        if (idList == null) return;
        for (var i = 0; i < idList.length; i++) {
          FileModel fileModel = upModelArray[i];
          fileModel.fileId = idList[i];
        }
        dealUploadList(model);
      }else{
        upModelArray.clear;
        toast(resp.msg);
      }     
    } catch (e) {
      upModelArray.clear;
      toast(LoginApi.ERROR_MSG);
    }

  }

  //处理上传数组
  dealUploadList(FormWidetModel model) {
    List cosUpList = [];
    for (var i = 0; i < upModelArray.length; i++) {
      FileModel fileModel = upModelArray[i];
      if (fileModel.fileId != null) {
        cosUpList.add(fileModel);
      }
      
    }
    if (cosUpList.isEmpty) {
      getRequiredContentIsDone();
    } else {
      startUploadFileList(cosUpList, model);
    }
  }

  startUploadFileList(List cosList, FormWidetModel model) async {
    if (cosList.isEmpty) {
      return;
    }
    upModelArray.clear();
    Get.loading();

    List fileIds = [];
    await CosManager().initPans();
    String? bucket = await CosManager().bucket();
    Future.wait(cosList.map((fileModel) async {
      if (fileModel is FileModel) {
        var result = await CosUploadHelper.nativeUpload(
            fileModel.savePath, fileModel.fileId, bucket);
        if (result != null) {
          fileIds.add(result);
        }
      }
      return;
    })).then((results) {
      _dealUploadData(fileIds, cosList,model);
      Get.dismiss();
    }).catchError((error) {
      Get.dismiss();
    });
  }

  _dealUploadData(List fileIds, List cosList, FormWidetModel model) {
    if (fileIds.isNotEmpty) {
      //上传成功
      for (FileModel cosFileModel in cosList) {
        bool isHave = false;
        for (String cosFileId in fileIds) {
          if (cosFileId == cosFileModel.fileId) {
            isHave = true;
            print('-----上传成功--${cosFileModel.filePath}');
            File saveFile = File(cosFileModel.savePath);
            cosFileModel.progress = 100;

            model.fileList.add(cosFileModel.toJson());
            model.contentList.add(cosFileModel.toUploadJson());
            dataList.refresh();
            if (model.contentList.length < 2) {
              getRequiredContentIsDone();
            }

            saveFile.delete();
          }
        }
        if (!isHave) {
          //失败的数据
        }
      }
    } else {
      if (model.contentList.length < 2) {
        getRequiredContentIsDone();
      }
     // toast('上传失败');
    }
  }

  void nextFocus() {
    if (focusNodeList.isEmpty) return;
    var size = focusNodeList.length;
    for (int i = 0; i < size; i++) {
      if (focusNodeList[i].hasFocus) {
        if (i + 1 == size) {
          focusNodeList[0].requestFocus();
        } else {
          focusNodeList[i + 1].requestFocus();
        }
      }
    }
  }

  void lastFocus() {
    if (focusNodeList.isEmpty) return;
    var size = focusNodeList.length;
    for (int i = 0; i < size; i++) {
      if (focusNodeList[i].hasFocus) {
        if (i == 0) {
          focusNodeList[size - 1].requestFocus();
        } else {
          focusNodeList[i - 1].requestFocus();
        }
      }
    }
  }

  //处理缓存
  dealCacheData() async {
    if (sendType == 1) {
      return;
    }
    String signKey = '$orgId-$modelId';
    List cache_dataList = [];
    for (var i = 0; i < dataList.length; i++) {
      List cache_modleList = dataList[i];
      List cache_mapList = [];
      for (var j = 0; j < cache_modleList.length; j++) {
        FormWidetModel formWidetModel = cache_modleList[j];
        FormWidetModel tempModel =
            FormWidetModel.fromJson(formWidetModel.toCopyJson(), isCache: 1);
        Map<String, dynamic> cacheMap = tempModel.toJson(isCache: 1);
        cache_mapList.add(cacheMap);
      }
      cache_dataList.add(cache_mapList);
    }
    Map<String, dynamic> cache_map = {
      'sign': signKey,
      'version': version,
      'data': cache_dataList
    };
    await UserDefault.setData(Define.APPROVE_SEND_CACHEKEY, cache_map);
  }

  //获取企业剩余空间
  _getCompanyCapacity() {
    DioUtil()
        .get('${PanApi.PAN_FILE_CAPACITY}/$orgId', null, true, () {})
        .then((data) {
      if (data == null) return;
      if (data['code'] == 1) {
      } else {
        toast('${data['msg']}');
      }
    });
  }
}
