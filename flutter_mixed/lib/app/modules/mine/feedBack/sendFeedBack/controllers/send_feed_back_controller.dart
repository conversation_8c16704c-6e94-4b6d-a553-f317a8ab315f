import 'package:dd_base_plugin/dd_base_plugin.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/api/LoginApi.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/cos/cos_manager.dart';
import 'package:flutter_mixed/app/common/cos/pan_datasource.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/file_extension.dart';
import 'package:flutter_mixed/app/extension/get_extension.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/gallery/gallery.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_attachment_model.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_file_model.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_upload_resp.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/send_feed_back_model.dart';
import 'package:flutter_mixed/app/modules/workStand/models/filemodel.dart';
import 'package:flutter_mixed/app/permission/permission_util.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:flutter_mixed/app/utils/image_util.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/app/utils/video_util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:dio/dio.dart' as dio;
import 'package:flutter_mixed/app/common/interceptor/upload_progress_interceptor.dart';

import '../../../../../../plugin/plugin_manage.dart';

class SendFeedBackController extends GetxController {
  //TODO: Implement SendFeedBackController

  TextEditingController? titleEditingController;
  TextEditingController? contentEditingController;
  FocusNode? titleNode;
  FocusNode? conetentNode;

  // 图片相关
  final RxList imageList = [].obs;
  final int maxImageCount = 6;

  // 存储上传请求ID，用于取消上传
  final Map<int, String> _uploadRequestIds = {};

  String? bucket;
  int maxFileSize = 60 * 1024 * 1024;

  @override
  void onInit() {
    super.onInit();
    titleEditingController = TextEditingController();
    contentEditingController = TextEditingController();
    titleNode = FocusNode();
    conetentNode = FocusNode();
  }

  @override
  void onReady() async {
    super.onReady();
    bucket = await CosManager().bucket();
  }

  //进入意见反馈历史记录
  jumpFeedBackHistory() {
    RouteHelper.route(Routes.FEED_BACK_LIST);
  }

  // 选择图片
  Future<void> pickImage() async {
    titleNode?.unfocus();
    conetentNode?.unfocus();
    var r = await PermissionUtil.checkGalleryPermission(Get.context!,
        tip: takePhotoPermissionTip);
    if (!r) return;

    if (imageList.length >= maxImageCount) {
      toast('最多上传$maxImageCount个附件');
      return;
    }

    final List<AssetEntity>? assets = await AssetPicker.pickAssets(Get.context!,
        pickerConfig: AssetPickerConfig(
            maxAssets: maxImageCount - imageList.length >= maxImageCount
                ? maxImageCount
                : maxImageCount - imageList.length,
            requestType: RequestType.common));
    if (assets == null) return;
    List uploadList = [];
    for (var i = 0; i < assets.length; i++) {
      AssetEntity entity = assets[i];
      File? file = await entity.file;
      FeedBackFileModel model = FeedBackFileModel();
      FileModel fileModel = FileModel();
      FileModel showFileModel = FileModel();
      fileModel.file = file;
      showFileModel.file = file;
      model.fileModel = fileModel;
      model.showFileMode = showFileModel;
      if (file.isVideo()) {
        if (file == null) continue;
        model.showFileMode?.file =
            await VideoUtil.getVideoFirstFrame(file.path);
      }
      model.isUploading = true;
      model.uploadProgress = 0.0;
      int modelIndex = imageList.length;
      imageList.add(model);
      uploadList.add(model);
      // if (file != null) {
      //   // 上传单个文件
      //   try {
      //     _uploadSingleImage(model, modelIndex);
      //   } catch (e) {
      //     model.isUploading = false;
      //     imageList.refresh();
      //     toast('图片上传失败');
      //   }
      // }
    }
    imageList.refresh();
    getFileIds(uploadList);
  }

  //获取fileId
  getFileIds(List upLoadList) async {
    try {
      List localList = [];
      for (var i = 0; i < upLoadList.length; i++) {
        FeedBackFileModel model = upLoadList[i];
        if (model.fileModel?.file.isVideo() == true) {
          if (StringUtil.isEmpty(model.showFileMode?.fileId) ||
              StringUtil.isEmpty(model.fileModel?.fileId)) {
            localList.add(model.fileModel);
            localList.add(model.showFileMode);
          }
        }
        if (model.fileModel?.file.isImage() == true &&
            StringUtil.isEmpty(model.fileModel?.fileId)) {
          localList.add(model.fileModel);
        }
      }
      if (localList.isEmpty) return;
      var panDatasource = PanDataSource(retrofitDio, baseUrl: Host.HOST);
      var resp = await panDatasource.getPanFields(localList.length);
      if (resp.success()) {
        List? idList = resp.data;
        if (idList == null) return;
        for (var i = 0; i < idList.length; i++) {
          FileModel fileModel = localList[i];
          fileModel.fileId = idList[i];
        }
        startUploadFileList(upLoadList);
      } else {
        toast('获取文件id失败');
      }
    } catch (e) {
      toast('获取文件id失败');
    }
  }

  // 删除图片
  void removeImage(FeedBackFileModel? model) {
    if (model != null) {
      if ((model.uploadProgress ?? 0) < 1) {
        if (model.task != null) {
          model.task?.cancel();
        }
      }
      imageList.remove(model);
    }
  }

  // 提交反馈
  void submitFeedback() async {
    String title = titleEditingController?.text.trim() ?? '';
    String content = contentEditingController?.text.trim() ?? '';

    if (title.isEmpty) {
      toast('请输入标题');
      return;
    }

    if (content.isEmpty) {
      toast('请输入反馈内容');
      return;
    }
    if (imageList.where((element) => element.uploadProgress < 1).isNotEmpty) {
      toast('有附件正在上传中，请稍后再试');
      return;
    }

    try {
      var datasource = WorkbenchDataSource(retrofitDio);

      SendFeedBackModel sendModel = SendFeedBackModel();
      //图片相关
      List<FeedBackAttachmentModel> attachments = [];
      for (var i = 0; i < imageList.length; i++) {
        FeedBackFileModel model = imageList[i];
        FeedBackAttachmentModel attachmentModel =
            FeedBackAttachmentModel.fromJson(model.toJson());
        attachmentModel.fileType =
            model.fileModel?.file.isImage() == true ? '0' : '1';
        attachments.add(attachmentModel);
      }
      sendModel.attachments = attachments;
      sendModel.content = content;
      sendModel.title = title;
      sendModel.device = await DeviceUtils.getDeviceName();
      sendModel.mobile = await UserHelper.getMobile();

      logger('=====上传参数===${sendModel.toJson()}');
      Get.loading();
      var resp = await datasource.submitFeedBack(sendModel);
      if (resp.success()) {
        toast('提交成功');
        Get.back();
      } else {
        toast(resp.msg);
      }
      Get.dismiss();
    } catch (e) {
      Get.dismiss();
      toast('提交失败');
    }
  }

  //删除上传成功后的文件
  _deleteFiles() {
    for (var i = 0; i < imageList.length; i++) {
      FeedBackFileModel model = imageList[i];
      if (model.fileModel?.file != null) {
        model.fileModel?.file?.delete();
      }
      if (model.fileModel?.file.isVideo() == true) {
        if (model.showFileMode?.file != null) {
          model.showFileMode?.file?.delete();
        }
      }
    }
  }

  //上传
  startUploadFileList(List upLoadList) async {
    if (upLoadList.isEmpty) {
      return;
    }
    await CosManager().initPans();
    for (var i = 0; i < upLoadList.length; i++) {
      FeedBackFileModel model = upLoadList[i];
      _uploadSingleImage(model);
    }
  }

  // 上传单个图片文件
  _uploadSingleImage(FeedBackFileModel fileModel) async {
    try {
      if (fileModel.fileModel?.file.isVideo() == true) {
        var videoPath =
            await VideoUtil.compressVideo(fileModel.fileModel!.file!.path);
        int videoFileSixe = await File(videoPath).getFileSize();
        if (videoFileSixe > maxFileSize) {
          toast('文件大小不能超过60M');
          return;
        }
        fileModel.fileModel?.file = File(videoPath);
        fileModel.fileName = fileModel.fileModel?.file?.path.split('/').last;
        fileModel.fileId = fileModel.fileModel?.fileId;
        //上传第一帧
        var coverFile = fileModel.showFileMode?.file;
        fileModel.firstFrameImageFileName = coverFile?.path.split('/').last;
        fileModel.firstFrameImageFileId = fileModel.showFileMode?.fileId;
        if (coverFile == null) {
          logger('获取第一帧失败');
          return;
        }
        var result = await CosUploadHelper.transferTaskUpload(
            coverFile.path, fileModel.showFileMode?.fileId, bucket);
        if (result != null) {}
      }
      if (fileModel.fileModel?.file.isImage() == true) {
        String tempPath = await saveFile(fileModel.fileModel!.file!);
        var compressPath = await basePlugin.compressImage(tempPath);
        if (compressPath == null) return;
        File tempFile = File(compressPath);
        int imaageFileSize = await tempFile.getFileSize();
        if (imaageFileSize > maxFileSize) {
          toast('文件大小不能超过60M');
          return;
        }
        fileModel.fileModel?.file = tempFile;
        fileModel.showFileMode?.file = tempFile;
        fileModel.fileName = compressPath.split('/').last;
        fileModel.fileId = fileModel.fileModel?.fileId;
      }

      var result = await CosUploadHelper.transferTaskUpload(
          fileModel.fileModel!.file!.path, fileModel.fileId, bucket,
          progress: (progress) {
        fileModel.uploadProgress = progress;
        if (progress >= 1) {
          fileModel.isUploading = false;
        }
        imageList.refresh(); // 刷新UI
      }, error: (fail) {
        if (fail != null) {
          fileModel.isUploading = false;
          fileModel.uploadProgress = 0.0;

          imageList.refresh();
        }
      });
      if (result != null) {
        fileModel.task = result;
      }
      imageList.refresh();
    } catch (e) {
      // 上传失败，更新状态
      fileModel.isUploading = false;
      fileModel.uploadProgress = 0.0;

      imageList.refresh();
      logger('图片上传异常: $e');
      toast('图片上传失败');
    }
  }

  //保存图片
  Future<String> saveFile(File file) async {
    String docment = (await getApplicationDocumentsDirectory()).path;
    String fileName = file.path.split('/').last;

    String startPath = '$docment/${Define.FEEDBACKIMAGESAVEPATH}';
    String filePath = '$startPath/$fileName';

    Directory directory = Directory(startPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    await file.copy(filePath);
    return filePath;
  }

  tapItem(FeedBackFileModel fileModel) {
    titleNode?.unfocus();
    conetentNode?.unfocus();
    if (fileModel.fileModel?.file.isImage() == true) {
      _tapImage(fileModel.fileModel!.file!.path);
    }
    if (fileModel.fileModel?.file.isVideo() == true) {
      _preivewFile(fileModel.fileModel!.file!.path);
    }
  }

  //查看图片
  _tapImage(String url) async {
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('', url));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
      return FadeTransition(
          opacity: animation, child: GalleryPage(galleryList, 0));
    }));
  }

  //查看视频
  _preivewFile(String path) async {
    CosHelper.openFile(path);
  }

  @override
  void onClose() {
    super.onClose();
    titleEditingController?.dispose();
    contentEditingController?.dispose();
    titleNode?.unfocus();
    conetentNode?.unfocus();
    _deleteFiles();
  }
}
