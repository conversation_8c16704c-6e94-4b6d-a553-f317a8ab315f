class FeedBackAttachmentModel {
  String? fileType; //0图片 1视频
  String? fileId;
  String? fileName;
  String? firstFrameImageFileId; //视频第一帧的fileId
  String? firstFrameImageFileName;

  FeedBackAttachmentModel(
      {this.fileType,
      this.fileId,
      this.fileName,
      this.firstFrameImageFileId,
      this.firstFrameImageFileName});

  FeedBackAttachmentModel.fromJson(Map<String, dynamic> json) {
    fileType = json['fileType'] ?? '0';
    fileId = json['fileId'] ?? '';
    fileName = json['fileName'] ?? '';
    firstFrameImageFileId = json['firstFrameImageFileId'] ?? '';
    firstFrameImageFileName = json['firstFrameImageFileName'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileType'] = fileType;
    data['fileId'] = fileId;
    data['fileName'] = fileName;
    data['firstFrameImageFileId'] = firstFrameImageFileId;
    data['firstFrameImageFileName'] = firstFrameImageFileName;
    return data;
  }
}
