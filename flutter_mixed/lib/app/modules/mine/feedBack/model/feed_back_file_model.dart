import 'dart:io';

import 'package:flutter_mixed/app/modules/workStand/models/filemodel.dart';
import 'package:tencentcloud_cos_sdk_plugin/transfer_task.dart';

class FeedBackFileModel {
  FileModel? fileModel;
  FileModel? showFileMode;
  String? fileId;
  String? tempFeedbackId;
  String? firstFrameImageFileId; //视频第一帧的fileId
  String? firstFrameImageTempFeedbackId;
  
  String? fileName;
  String? firstFrameImageFileName;//视频第一帧文件名称
  double? uploadProgress; // 上传进度 0.0-1.0
  bool? isUploading; // 是否正在上传
  TransferTask? task;

  FeedBackFileModel({this.tempFeedbackId, this.fileId,this.fileName, this.uploadProgress = 0.0, this.isUploading = false});

  FeedBackFileModel.fromJson(Map<String, dynamic> json){
    fileId = json['fileId'] ?? '';
    tempFeedbackId = json['tempFeedbackId'] ?? '';
    firstFrameImageFileId = json['firstFrameImageFileId'] ?? '';
    firstFrameImageTempFeedbackId = json['firstFrameImageTempFeedbackId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['firstFrameImageFileId'] = firstFrameImageFileId;
    data['fileName'] = fileName;
    data['firstFrameImageFileName'] = firstFrameImageFileName;
    return data;
  }
}