import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_detail_url_model.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_reply_model.dart';

class FeedBackDetailModel {
  String? feedbackId;
  String? title;
  String? content;
  int? createdAt;//创建时间
  String? status; //0 待回复 1已回复

  List<FeedBackDetailUrlModel?>? attachments;//附件url
  FeedBackReplyModel? replies;

  FeedBackDetailModel({
    this.feedbackId,
    this.title,
    this.content,
    this.createdAt,
    this.status,
    this.attachments,
    this.replies
  });

  FeedBackDetailModel.fromJson(Map<String, dynamic> json) {
    feedbackId = json['id'] ?? '';
    title = json['title'] ?? '';
    content = json['content'] ?? '';
    createdAt = json['createdAt'];
    status = json['status'] ?? '0';
    if (json['attachments'] != null) {
      attachments = <FeedBackDetailUrlModel>[];
      json['attachments'].forEach((v) {
        attachments!.add(FeedBackDetailUrlModel.fromJson(v));
      });
    }
    replies = json['replies'] != null ? FeedBackReplyModel.fromJson(json['replies']):null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = feedbackId;
    data['title'] = title;
    data['content'] = content;
    data['createdAt'] = createdAt;
    data['status'] = status;
    data['attachments'] = attachments?.map((v) => v?.toJson()).toList();
    data['replies'] = replies?.toJson();
    return data;
  }
}
