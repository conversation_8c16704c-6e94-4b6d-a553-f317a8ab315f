class FeedBackReplyModel {
  String? replyContent;
  int? replyTime;

  FeedBackReplyModel({
    this.replyContent,
    this.replyTime,
  });

  FeedBackReplyModel.fromJson(Map<String, dynamic> json) {
    replyContent = json['replyContent'] ?? '';
    replyTime = json['replyTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['replyContent'] = replyContent;
    data['replyTime'] = replyTime;
    return data;
  }
}
