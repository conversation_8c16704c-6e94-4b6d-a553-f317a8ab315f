import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_attachment_model.dart';

class FeedBackListModel {
  String? feedbackId;
  String? title;
  String? content;
  int? createdAt;
  String? status;//0 待回复 1已回复

  FeedBackListModel({
    this.feedbackId,
    this.title,
    this.content,
    this.createdAt,
    this.status,
  });

  FeedBackListModel.fromJson(Map<String, dynamic> json) {
    feedbackId = json['id'] ?? '';
    title = json['title'] ?? '';
    content = json['content'] ?? '';
    createdAt = json['createdAt'] ?? 0;
    status = json['status'] ?? '0';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = feedbackId;
    data['title'] = title;
    data['content'] = content;
    data['createdAt'] = createdAt;
    data['status'] = status;
    return data;
  }

  getStatusStr() {
    if (status == '0') {
      return '已收到';
    } else {
      return '已回复';
    }
  }

  getStatusTextColor(){
    if (status == '0') {
      return ColorConfig.msgTextColor;
    } else {
      return ColorConfig.themeCorlor;
    }
  }

  getStatusBackGroudColor(){
    if (status == '0') {
      return ColorConfig.mybackgroundColor;
    } else {
      return ColorConfig.imMyBackColor;
    }
  }
}