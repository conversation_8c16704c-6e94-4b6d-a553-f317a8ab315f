class FeedBackDetailUrlModel {
  String? fileType;
  String? fileUrl;
  String? firstFrameImageType;
  String? firstFrameImageUrl;

  FeedBackDetailUrlModel({
    this.fileType,
    this.fileUrl,
    this.firstFrameImageType,
    this.firstFrameImageUrl,
  });

  FeedBackDetailUrlModel.fromJson(Map<String, dynamic> json) {
    fileType = json['fileType'] ?? '0';
    fileUrl = json['fileUrl'] ?? '';
    firstFrameImageType = json['firstFrameImageType'] ?? '0';
    firstFrameImageUrl = json['firstFrameImageUrl'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['fileType'] = fileType;
    data['fileUrl'] = fileUrl;
    data['firstFrameImageType'] = firstFrameImageType;
    data['firstFrameImageUrl'] = firstFrameImageUrl;
    return data;
  }
}