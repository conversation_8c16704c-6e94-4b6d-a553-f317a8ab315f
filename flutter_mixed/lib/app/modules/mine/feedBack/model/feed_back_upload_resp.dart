//上传附件返回
class FeedBackUploadResp {
  String? fileId;
  String? tempFeedbackId;

  FeedBackUploadResp({this.tempFeedbackId, this.fileId});

  FeedBackUploadResp.fromJson(Map<String, dynamic> json){
    fileId = json['fileId'] ?? '';
    tempFeedbackId = json['tempFeedbackId'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['tempFeedbackId'] = tempFeedbackId;
    return data;
  }
}