import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_attachment_model.dart';

class SendFeedBackModel {
  List<FeedBackAttachmentModel?>? attachments;
  String? content;
  String? title;
  String? device;
  String? mobile;

  SendFeedBackModel({
    this.attachments,
    this.content,
    this.title,
    this.device,
    this.mobile,
  });

  SendFeedBackModel.fromJson(Map<String, dynamic> json) {
    if (json['attachments'] != null) {
      attachments = <FeedBackAttachmentModel>[];
      json['attachments'].forEach((v) {
        attachments!.add(FeedBackAttachmentModel.fromJson(v));
      });
    }
    content = json['content'] ?? '';
    title = json['title'] ?? '';
    device = json['device'] ?? '';
    mobile = json['mobile'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['attachments'] = attachments?.map((v) => v?.toJson()).toList();
    data['content'] = content;
    data['title'] = title;
    data['device'] = device;
    data['mobile'] = mobile;
    return data;
  }
}