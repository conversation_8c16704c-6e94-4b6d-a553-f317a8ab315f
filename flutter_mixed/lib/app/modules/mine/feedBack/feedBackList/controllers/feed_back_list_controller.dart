import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_list_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';

class FeedBackListController extends GetxController {
  //TODO: Implement FeedBackListController

  RxList dataList = [].obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    _getMyFeedBack();
  }

  //获取反馈历史记录
  _getMyFeedBack() async {
    try {
      var datasource = WorkbenchDataSource(retrofitDio);
      var resp = await datasource.getMyFeedBack();
      if (resp.success()) {
        if (resp.data != null) {
          dataList.value = resp.data ?? [];
        }
      }
    } catch (e) {
      logger('Error in getMyFeedBack: $e');
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  jumpFeedBackDetail(FeedBackListModel listModel){
    RouteHelper.route(Routes.FEED_BACK_DETAIL,arguments: {'feedbackId':listModel.feedbackId});
  }

}
