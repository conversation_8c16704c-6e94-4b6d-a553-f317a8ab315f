import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/base_info/info.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/extension/number_size_ex.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_list_model.dart';
import 'package:flutter_mixed/res/assets_res.dart';

import 'package:get/get.dart';

import '../controllers/feed_back_list_controller.dart';

class FeedBackListView extends GetView<FeedBackListController> {
  const FeedBackListView({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar:
              TitleBar().backAppbar(context, '历史记录', false, [], onPressed: () {
            Get.back();
          }),
          body: controller.dataList.isEmpty
              ? _buildEmptyWidget()
              : Column(
                  children: [
                    16.gap,
                    Expanded(
                        child: ListView.builder(
                      itemCount: controller.dataList.length,
                      itemBuilder: (context, index) {
                        FeedBackListModel? listModel = controller.dataList[index];
                        if (listModel == null) {
                          return Container();
                        }
                        return InkWell(
                          overlayColor: const WidgetStatePropertyAll(Colors.transparent),
                          onTap: () {
                            controller.jumpFeedBackDetail(listModel);
                          },
                          child: Container(
                            margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: ColorConfig.whiteColor),
                            padding: const EdgeInsets.all(12),
                            child: _buildItem(listModel),
                          ),
                        );
                      },
                    ))
                  ],
                ),
        ));
  }

  _buildItem(FeedBackListModel listModel) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Container(
              child:  Text(
                listModel.title ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: ColorConfig.mainTextColor),
              ),
            )),
            8.gap,
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 2),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: listModel.getStatusBackGroudColor()),
              child: Text(
                listModel.getStatusStr(),
                style: TextStyle(fontSize: 12, color: listModel.getStatusTextColor()),
              ),
            )
          ],
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          alignment: Alignment.centerLeft,
          child: Text(
            listModel.content ?? '',
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(fontSize: 14, color: ColorConfig.mainTextColor),
          ),
        ),
        Container(
          alignment: Alignment.centerLeft,
          child: Text(
            BaseInfo().formatTimestamp(listModel.createdAt, 'yyyy-MM-dd HH:mm:ss'),
            style: const TextStyle(fontSize: 12, color: ColorConfig.desTextColor),
          ),
        ),
      ],
    );
  }

  //无数据
  _buildEmptyWidget() {
    return Container(
      width: double.infinity,
      height: 320,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            AssetsRes.FEED_BACK_LIST_NON,
            width: 140,
          ),
          const Text("暂无进度",
              style: TextStyle(color: ColorConfig.msgTextColor, fontSize: 14)),
        ],
      ),
    );
  }
}
