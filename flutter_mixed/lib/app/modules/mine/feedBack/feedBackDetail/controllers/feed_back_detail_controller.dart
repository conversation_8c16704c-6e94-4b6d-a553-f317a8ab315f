import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_mixed/app/common/api/Define.dart';
import 'package:flutter_mixed/app/common/user/user_helper.dart';
import 'package:flutter_mixed/app/common/widgets/widgets.dart';
import 'package:flutter_mixed/app/im/route/route_helper.dart';
import 'package:flutter_mixed/app/modules/gallery/gallery.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_detail_model.dart';
import 'package:flutter_mixed/app/modules/mine/feedBack/model/feed_back_detail_url_model.dart';
import 'package:flutter_mixed/app/retrofit/datasource/workbench_datasource.dart';
import 'package:flutter_mixed/app/routes/app_pages.dart';
import 'package:flutter_mixed/app/utils/cos_helper.dart';
import 'package:flutter_mixed/app/utils/http.dart';
import 'package:flutter_mixed/app/utils/string-util.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:flutter_mixed/main.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:path_provider/path_provider.dart';

class FeedBackDetailController extends GetxController {
  //TODO: Implement FeedBackDetailController

  // 反馈详情数据
  RxString status = '0'.obs;
  String? feedbackId;
  FeedBackDetailModel? detailModel;
  String? myAvatar;
  String? myName;
  List imageList = [];
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments['feedbackId'] != null) {
      feedbackId = Get.arguments['feedbackId'];
    }
    _getUserInfo();
    _getFeedBackDetail();
  }

  _getUserInfo() async {
    myAvatar = await UserHelper.getOwnAvatar();
    myName = await UserHelper.getOwnName();
  }

  //获取反馈详情
  _getFeedBackDetail() async {
    if (StringUtil.isEmpty(feedbackId)) return;
    try {
      logger('=====feedbackId===$feedbackId');
      var datasource = WorkbenchDataSource(retrofitDio);
      var resp = await datasource.getFeedBackDetail(feedbackId!);
      if (resp.success()) {
        detailModel = resp.data;
        _dealImageList();
        status.value = detailModel?.status ?? '0';
        logger('=====detail===${detailModel?.toJson()}');
        status.refresh();
      } else {
        toast(resp.msg);
      }
    } catch (e) {
      logger('Error in getMyFeedBack: $e');
    }
  }

  //处理显示图片
  _dealImageList() {
    if (detailModel?.attachments == null) {
      return;
    }
    for (var i = 0; i < detailModel!.attachments!.length; i++) {
      FeedBackDetailUrlModel? urlModel = detailModel!.attachments![i];
      String? url = urlModel?.fileUrl;
      if (urlModel?.fileType == '1') {
        url = urlModel?.firstFrameImageUrl;
      }
      imageList.add(url);
      getDownLoadPath(url, i, true);
    }
  }

  //获取下载地址
  getDownLoadPath(String? url, int index, bool isImage,
      {bool isOpen = false}) async {
    if (StringUtil.isEmpty(url)) return;
    String endPath = _getEndPath(url!);

    if (isImage) {
      endPath = '$endPath.jpg';
    } else {
      endPath = '$endPath.mp4';
    }
    logger('=====endPath==$endPath');
    String docment = (await getApplicationDocumentsDirectory()).path;
    String startSavePath = Define.FEEDBACKFILESAVEPATH;
    String startLoadPath = Define.FEEDBACKFILELOADTPATH;
    if (isImage) {
      startSavePath = Define.FEEDBACKIMAGESAVEPATH;
      startLoadPath = Define.FEEDBACKIMAGELOADTPATH;
    }
    String savePath = '$docment/$startSavePath/$endPath';
    String loadPath = '$docment/$startLoadPath/$endPath';
    File saveFile = File(savePath);
    if (saveFile.existsSync()) {
      if (isImage) {
        imageList[index] = savePath;
        status.refresh();
      }
      if (isOpen) {
        preivewFile(savePath);
      }
    } else {
      Directory directory = Directory('$docment/$startSavePath');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      Directory loadDirectory = Directory('$docment/$startLoadPath');
      if (!await loadDirectory.exists()) {
        await loadDirectory.create(recursive: true);
      } else {
        File loadFile = File(loadPath);
        if (loadFile.existsSync()) {
          await loadFile.delete();
        }
      }
      downLoadFiel(url, index, isImage, savePath, loadPath, isOpen: isOpen);
    }
  }

  _getEndPath(String url) {
    List fileIdList = url.split('?');
    String startStr = fileIdList.first;
    List startList = startStr.split('/');
    String endPath = '${startList.last}';
    return endPath;
  }

  //查看图片
  tapImage(String url) async {
    var galleryList = <GalleryItem>[];
    galleryList.add(GalleryItem('', url));
    Navigator.push(Get.context!,
        PageRouteBuilder(pageBuilder: (ctx, animation, secondaryAnimation) {
      return FadeTransition(
          opacity: animation, child: GalleryPage(galleryList, 0));
    }));
  }

  //查看视频
  preivewFile(String path) async {
    CosHelper.openFile(path);
  }

  //下载附件并查看
  downLoadFiel(String url, int index, bool isImage, String savePath, loadPath,
      {bool isOpen = false}) {
    try {
      DioUtil().downLoadFile(url, savePath, loadPath, () {
        toast('获取文件失败');
      }, (finishPath) {
        if (isImage) {
          imageList[index] = finishPath;
        }
        if (isOpen) {
          preivewFile(finishPath);
        }
      });
    } catch (e) {
      toast('获取文件失败');
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
