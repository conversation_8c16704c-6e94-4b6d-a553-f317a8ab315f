import 'package:dio/dio.dart';

/// 上传进度拦截器
class UploadProgressInterceptor extends Interceptor {
  /// 进度回调函数映射，key为请求的唯一标识
  static final Map<String, ProgressCallback> _progressCallbacks = {};
  
  /// 注册进度回调
  static void registerProgressCallback(String requestId, ProgressCallback callback) {
    _progressCallbacks[requestId] = callback;
  }
  
  /// 移除进度回调
  static void removeProgressCallback(String requestId) {
    _progressCallbacks.remove(requestId);
  }
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 检查是否有注册的进度回调
    String? requestId = options.extra['upload_progress_id'];
    if (requestId != null && _progressCallbacks.containsKey(requestId)) {
      // 设置上传进度回调
      options.onSendProgress = (int sent, int total) {
        _progressCallbacks[requestId]?.call(sent, total);
      };
    }
    
    super.onRequest(options, handler);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 请求完成后清理回调
    String? requestId = response.requestOptions.extra['upload_progress_id'];
    if (requestId != null) {
      removeProgressCallback(requestId);
    }
    
    super.onResponse(response, handler);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 请求出错后清理回调
    String? requestId = err.requestOptions.extra['upload_progress_id'];
    if (requestId != null) {
      removeProgressCallback(requestId);
    }
    
    super.onError(err, handler);
  }
}

/// 进度回调函数类型定义
typedef ProgressCallback = void Function(int sent, int total);
