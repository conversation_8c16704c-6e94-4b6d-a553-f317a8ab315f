import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mixed/app/common/config/config.dart';
import 'package:flutter_mixed/logger/logger.dart';

import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

import '../../../widgets/widgets.dart';
import '../controllers/common_scroll_pic_controller.dart';


class CommonScrollPicView extends StatelessWidget {
  const CommonScrollPicView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConfig.blackColor,
      appBar: TitleBar().backAppbar(context, '查看图片', true, [], onPressed: () {
      }),
      body: GetBuilder<CommonScrollPicController>(builder: (controller){
        return InkWell(
          onTap: (){
            Get.back();
          },
          overlayColor: const WidgetStatePropertyAll(Colors.transparent),
          child: Column(
          children: [
            Expanded(child: PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                String item = controller.imageUrls[index];

                ImageProvider image = ExtendedFileImageProvider(File(item));
                if (item.startsWith('http')) {
                  image = ExtendedNetworkImageProvider(item);
                }
                return PhotoViewGalleryPageOptions(
                  imageProvider: image,
                  initialScale: PhotoViewComputedScale.contained * 0.9,
                  heroAttributes: PhotoViewHeroAttributes(tag: controller.imageUrls[index])
                );
              },
              itemCount: controller.imageUrls.length,
              onPageChanged: (int index) {
                logger('----$index');

                controller.currentIndex = index;
                controller.update();
              },
              pageController:
              PageController(initialPage: controller.currentIndex),
              backgroundDecoration:
              const BoxDecoration(color: ColorConfig.whiteColor),
            )),
            Container(
              color: Colors.white,
              alignment: Alignment.bottomCenter,
              padding: EdgeInsets.only(bottom: 10),
              child: Text('${controller.currentIndex + 1}/${controller.imageUrls.length}' , style: TextStyle(fontSize: 15 , color: Colors.black),),
            ),

          ],
        ),
        );
      }),
    );
  }
}
