

import '../../utils/storage.dart';
import '../api/Define.dart';

class UserHelper {

  static Future<String> getUid() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    var myUserId = tokenInfo['userId'];
    return myUserId ?? '';
  }

  static Future<String> getOwnName() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    return tokenInfo['name'] ?? '';
  }

  static Future<String> getOwnAvatar() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    return tokenInfo['avatar'] ?? '';
  }

  static Future<String> getOwnImId() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    return tokenInfo['imId'] ?? '';
  }

  static Future<String> getAccessToken() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    return tokenInfo['access_token'] ?? '';
  }

  static Future<String> getMobile() async {
    dynamic tokenInfo = await UserDefault.getData(Define.TOKENKEY);
    if(tokenInfo == null) return "";
    var mobile = tokenInfo['mobile'];
    return mobile ?? '';
  }
}