import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_mixed/logger/logger.dart';
import 'package:get/get_connect/http/src/response/response.dart' as prefix;

import '../env/env_local.dart';

class Host {
  // 担当4.0开发环境
  static String MEETINGLINK = 'meeting-h5/app/index.html#';
  //担当4.0测试环境
  static String HOST = 'https://api.test.ddbes.com/';
  static String TOKENHOST = 'https://auth.test.ddbes.com/';
  static String ROUTERHOST = 'http://test.router.ddbes.com:8080/';
  static String WEBHOST = 'http://test.mobile.ddbes.com';
  static String SHORTHOST = 'test.mobile.ddbes.com';
  static String IM_OFFLINE_HOST = 'http://*********:8089/';

  //开发环境
  // static const HOST = 'http://dev.inddbes.com/';
  // static const TOKENHOST = 'http://passport.inddbes.com/';
  // static const ROUTERHOST = 'http://router.im.inddbes.com/';
  // static const WEBHOST = 'http://test.mobile.ddbes.com/';
  // static const SHORTHOST = 'mobile.inddbes.com';

//担当4.0正式环境
  // static const HOST = 'https://api.ddbes.com/';
  // static const TOKENHOST = 'https://passport.ddbes.com/';
  // static const ROUTERHOST = 'https://router.im.ddbes.com/';
  // static const WEBHOST = 'https://mobile.ddbes.com';
  //static const SHORTHOST = 'mobile.ddbes.com';

  static const APPVERSION = '5.0.5';
  static const CLIENTCODE = '403';
  static const APPUPDATECONTENT = '1.更新了UI样式\n2.增加了会话中草稿保存功能\n3.修改了一些bug，优化了用户体验';

  static const TELLIST = ['15132686177', '13273612065'];

  static String token = '';
}

class WeChat {
  static const WXAPPID = "wxf55e2c3e1dcd35fc";
  static const WXAPPSECRET = "5e94ec8b5939cbad04fae4139b9bdf1d";

  // 微信登录验证
  Future<Response> wechatLoginAuth(String code) async {
    return await Dio().get(
      "https://api.weixin.qq.com/sns/oauth2/access_token",
      queryParameters: {
        "appid": WXAPPID,
        "secret": WXAPPSECRET,
        "code": code,
        "grant_type": "authorization_code"
      },
    );
  }

  // 微信用户信息
  Future<Response> getWechatUserInfo(String accessToken, String openid) async {
    return await Dio().get(
      "https://api.weixin.qq.com/sns/userinfo",
      queryParameters: {
        "access_token": accessToken,
        "openid": openid,
      },
    );
  }
}

class LoginApi {
  static const ERROR_MSG = '网络有点卡响应超时，请稍后重试!';
  static const API_VERSION = 'v2';
  static const CHECK_PHONE_CODE =
      'verify/api/v2/sms/verify/phone/'; //type:1 发送担当开放平台的绑定注册号码 2绑定手机号 3解除绑定手机号 4找回密码 5修改密码 6担当邀请用户 7用户登录 8解散企业 9更换手机号 10验证码分享文件 11申请加入企业 12转让企业 13微信绑定担当 14账号新设备登录
  static const GET_IMAGE_CODE = 'verify/api/v2/verify/getVerifyImg/phone/';
  static const CHECK_IMAGE_SEND_CODE =
      'verify/api/v2/verify/verify/img/send/code';
  static const GETSMSCODE = "verify/api/v2/sms/"; //发送验证码

  static const CODEVERIFYTOKEN = 'oauth/verify/token'; //验证码登录获取token
  static const MOBILEGETTOKEN = 'oauth/mobile/token'; //密码登录获取token
  static const WECHATGETTOKEN = "oauth/openid/token"; //微信获取token
  static const PLAT_OPEN_SCAN = "oauth/qrcode/v1"; //担当app扫描二维码
  static const PLAT_OPEN_DEAL = "oauth/qrcode/v1/confirm"; //担当二维码确定/取消

  static const REGISTERURL = "user/user/register/v1/info/complete"; //注册
  static const VERIFYCODEURL = "user/user/register/v1"; //校验注册验证码
  static const FORGETPWDURL = "user/user/set/forgetPwd"; //忘记密码
  static const VERIFYWXDDURL = "user/wx/volidate/"; //验证手机号是否绑定担当
  static const VERIFYWXCODEURL = "user/wx/bind"; //校验微信绑定验证码
  static const VERIFYTELURL = 'user/user/volidate'; //验证手机号是否注册
  static const PHONE_SEARCH_USER = "user/user/info/search";
  static const MAKESHORTURL = "user/shortUrl"; //生成短链接

  ///根据手机号搜索用户
  static const GETPERSALINFO = "user/user/info"; //获取个人资料
  static const UPDATEUSERINFO = "user/user/upUser"; //个人资料更改
  static const CONTACTMATCH = "user/user/phone/matching"; //通讯录匹配
  static const IM_USER_INFO = "user/user/info/v2"; //根据userId查详情
  static const ABOUTFRIEND = "user/friend"; //好友相关
  static const ISALLOWFRIEND = "user/friend/volidate"; //是否允许加为好友

  static const FILE_GET_SETTING = "user/upload/config"; //获取腾讯云配置
  static const SIX_MERGE_DATA = "user/user/start/v2"; //6合一

  static const APP_BADGE_UPLOAD = "user/badge/v1"; //上报badge给服务端

  static const GETINFOWITHIMID = "relation/groups/v1/topChat"; //获取用户信息
  static const IM_FRIEND_APPLY = "relation/apply/v1"; //添加好友
  static const IM_FRIENDS_VERSION = "relation/friends/v1/version"; // 获取好友版本
  static const IM_FRIENDS_LIST = "relation/friends/v1"; // 获取好友列表,修改好友备注,删除好友
  static const IM_GROUP_TYPE = "relation/groups/v1/type"; // {type} 获取我的群组
  static const IM_GROUP_CREATE = "relation/groups/v1"; // 创建群组
  static const IM_GROUP_QR_CODE = "relation/groups/v3/code"; // 群组二维码相关 
  static const IM_GROUP_QR_CODE_JOIN = "relation/groups/v3/scan"; // /qr 扫描二维码进群 
  static const IM_GROUP_MEMBER =
      "relation/groups/v1/member"; // 邀请好友或公司成员进群,删除群组成员
  static const IM_FRIEND_APPLY_INFO =
      "relation/apply/v1/record/info"; // /{applyId}获取好友申请详情
  static const IM_USER_MINE = "relation/im/users/v1/mine"; // 获取当前用户IM服务的数据

  static const PROTOCAOLURL = "https://mobile.ddbes.com/h5/service.html"; //服务协议
  static const PRIVACYURL = "https://mobile.ddbes.com/h5/privacy.html"; //隐私协议
  static const COMPLAINT_INSTRUCTIONS = "https://mobile.ddbes.com/h5/complaint.html"; //投诉须知
  static const PRIVACYURL_ANDROID = "https://mobile.ddbes.com/h5/privacy_android.html"; // 隐私协议

  static const LOG_REQUEST = "statistic/statistic/record"; //网络请求log记录

  static const VERIFY_IDCARD = "user/user/verify/v1/idcard/name"; //提交身份证号和姓名
  static const REALNAME_UPLOAD = "user/user/verify/v1/idcard/pic"; //提交身份证正反⾯和本⼈照⽚
  static const REALNAME_VERIFY_STATUS = "user/user/verify/v1/status"; //获取认证状态
  static const REALNAME_COS_TOKEN = "user/user/verify/v1/sts"; //获取腾讯云token

  static String privacyUrl() {
    if (Platform.isAndroid) return PRIVACYURL_ANDROID;
    return PRIVACYURL;
  }

  static String complatinIns() {
    return COMPLAINT_INSTRUCTIONS;
  }
}

class IMApi {
  static const IM_USER_MINE = "im/im/v1/mine/platform"; // 获取当前用户IM服务的数据
  static const GET_COS_TOKEN = "im/im/v1/cos/token"; // 获取腾讯云临时秘钥
}

class ORGApi {
  static const CREATECOMPANY = "org/company/v2"; //创建公司
  static const GETCOMPANYINDUSTRY = "org/industry/company"; //获取所有公司行业
  static const GETCOMPANYTYPE = "org/company/type"; //获取公司类型
  static const SEARCHCOMPANY = "org/company"; //GET搜索公司
  static const GET_ALL_COMPANY_INFO =
      "org/company/app/v3/user"; // 所有企业信息 v3提醒通讯录未处理
  static const GET_ALL_COMPANY_UNTREATED =
      "org/company/app/user/untreated/v2"; // 所有企业未读红点//org/company/app/v3/user/untreated
  static const PERSONOUTORG = "org/personnel/v2/schedule"; // 查询部门,成员的数据
  static const EDITDEPT = 'org/personnel/principal/dept/info/v1'; //编辑部门
  static const DELETEDEPT = 'org/company/dept/v2'; //删除部门
  static const GETCOMPANYINFO = "org/company"; //公司详情
  static const JOINORG = "org/personnel/v3/schedule/apply"; // 个人申请加入企业
  static const DEPTADD = "org/company/dept/child/v2"; //创建部门
  static const DELETEORG =
      'org/company/v3/companyId'; //{companyId}/phone/{phone}/code/{code}删除公司

  static const USERSMOVE =
      'org/personnel/v3/admin/schedule/deptement'; //企业关系调度-批量转移人员
  static const LEAVELCOMPANY =
      'org/personnel/v3/admin/schedule/leave'; //企业关系调度-批量请离人员
  static const GETALLSTAFFSORT =
      "org/personnel/schedule/member/sort"; //获取公司下的所有员工(按字母排好序的)

  static const GETWAITDETAIL = "org/personnel/schedule/wait/v2"; //我的申请详情
  static const GETWAITLIST =
      "org/personnel/v4/schedule/wait"; //我的申请未处理数量  {拼接参数分页获取待处理数据}
  static const EXTERNAL_CONTACT_LIST =
      "org/v2/company/external/contacts/info"; //企业-查询外部协作人员列表
  static const ADD_PUT_EXTERNAL_CONTACT =
      "org/company/admin/external/contacts"; //添加、修改、删除外部协作人员
  static const EXTERNAL_CONTACT_DETAIL =
      "org/v2/company/external/contacts"; //获取外部联系人详情

  static const POST_ACTIVE_MESSAGE =
      "org/company/admin/external/contacts/message"; // 企业-主动推送激活消息
  static const ORG_SEARCH_USER =
      'org/company/user/fuzzy/keyword'; //  /{keyword}/page/{page}/size/{pageSize}?org=$keyword//企业-通讯录模糊查询人名

  static const CHANGEORGCREATER = "org/company/v2/attorn"; //转让企业创建者身份

  static const CHANGEORGDETAIL = "org/company/admin"; //修改企业信息
  static const GETDEPTS = "org/company/dept/exist/v1"; //获取所在所有部门

  static const COMPANYUSERINFO =
      'org/visibility/field/card/v2/userId'; // /userId
  static const POST_EXTERNAL_HAND =
      "org/company/external/contacts/hand"; // 企业-处理邀请外部联系人
  static const AGREEJOIN = "org/personnel/v2/schedule/agree"; // 企业同意个人加入
  static const REFUSEJOIN = "org/personnel/schedule/refuse"; //企业拒绝个人加入
  static const IGNORERECORD = "org/personnel/schedule/ignore/v1"; //企业忽略个人加入
  static const GETORGUSERS = "org/personnel/member/v1"; //获取公司所有人员

  static const GET_WORK_TOOL = "org/company/v4/group"; // {companyId}工作页功能模块
  static const GET_FUNC_CACHE = "org/company/v2/group"; //{companyId}工作页功能模块缓存

  static const GET_FUNC_UNREADCOUNT =
      "org/company/group/untreated/message/v3"; //{companyId}工作页功能模块未读数//org/company/v2/group/message

  static const GET_ALL_CHILDORGS = "org/company/child/v1"; //获取集团下所有分公司

  static const GET_ORG_PERMISSIONS = "org/company/manager"; //获取公司权限列表

  static const PERMISSIONS_EDIT = "org/company/manager/v2"; //权限设置

  static const AI_ENTRY_LIST = "org/company/ai/tmp/v1"; //ai入口

  static const GET_TOP_FUNCTION = "org/company/pc/group/use"; //获取常用应用
  
}

class ApproveApi {
  static const APPROVEMODELLIST = 'approve/model/homepage/group/orgid'; //审批模版
  static const APPROVEMODFORM = 'approve/model/modelform/master/modelid';

  ///{modelId} 发起审批表单
  static const APPROVEPROCESS = 'approve/model/process/preview';

  ///{modelId} 发起审批表单
  static const APPROVECREATE = 'approve/instance/v2'; //创建审批
  static const APPROVEVERIFYLIMIT = 'approve/instance/verify/widget'; //审批签字上传回调
  static const APPROVELIST = 'approve/instance/list'; //审批列表
  static const APPROVELIST_NEW = 'approve/instance/list/v3'; //全部模式审批列表
  static const APPROVELIST_SEARCH = 'approve/instance/list/search'; //审批搜索
  static const APPROVELISTALLMODEL =
      'approve/model/list/model/name'; // /{orgId}获取审批列表模版所有可筛选
  static const APPROVEFLOW = 'approve/instance/flow'; //审批同意或拒绝
  static const APPROVEFLOW_V2 = 'approve/instance/flow/v2'; //审批同意或拒绝v2
  static const APPROVEINFO =
      'approve/instance/approveinfo'; // /{approveId} 审批详情
  static const APPROVEVERIFYADDITIONAL =
      'approve/instance/verify/additional'; // 校验当前可选的加审方式
  static const APPROVEADDITIONAL = 'approve/instance/additional'; // 确定加审
  static const APPROVEWITHDRAW =
      'approve/instance/withdraw'; // /{approveId} 撤回审批
  static const APPROVEURGING = 'approve/instance/urge'; // /{approveId} 催办审批
  static const APPROVECOMMENT =
      'approve/instance/comment'; // /{approveId}获取评论 增加评论
  static const APPROVEALLUNREAD = 'approve/redpoint/all'; // /{orgId}获取未读
  static const APPROVEHOMEPAGEUNREAD =
      'approve/redpoint/homepage'; // /{orgId}获取审批首页未读
  static const APPROVEHOMEPAGEUNREADV2 =
      'approve/redpoint/homepagev2'; // 获取审批首页未读
  static const APPROVEALLSIGNREAD =
      'approve/instance/read/all'; //DELETE请求  /orgid/{orgId}/listtype/{listtype}批量已读
  static const APPROVEALLSIGNREAD_v2 =
      'approve/instance/read/all/v2'; //DELETE请求  /orgid/{orgId}/listtype/{listtype}批量已读v2
  static const APPROVETRANSACT = 'approve/instance/transact'; //审批详情-办理人提交办理

  static const APPROVELASTSIGN = 'approve/instance/last/signature'; //获取上次签名
  static const APPROVESIGNKEY = 'approve/instance/sign/key';

  ///{orgId} 审批签字获取key
  static const APPROVESIGNCODE = 'approve/instance/signature/file'; //审批签字上传回调

  static const ALLAPPROVEUNREAD = "approve/redpoint/multi/org"; // 获取所有公司审批未读数

  static const APPROVEPDFPREVIEW = "approve/instance/pdf/single"; //获取预览审批pdf

  static const APPROVEEXPORT = "approve/instance/pdf/push/mail"; //导出到邮箱

  static const APPROVEBATCH = "approve/instance/batch/flow"; //批量审批
  static const APPROVEBATCH_V2 = "approve/instance/batch/flow/v2"; //批量审批v2

  static const APPROVERESUBMIT =
      "approve/instance/resubmit/verify"; //校验是否可以重新发起

  static const APPROVEPENDINGSCREENDATA =
      "approve/instance/listpage/count"; //审批全部公司模式的筛选数据/{listType}
  static const APPROVESEARCHORGDATA =
      "approve/instance/listpage/count/search"; //审批全部公司模式的筛选数据/{listType}
  static const APPROVEALLORGDATA =
      "approve/instance/listpage/org"; //审批全部公司列表 /{listType}
  static const APPROVEMODELSCREENDATA =
      "approve/instance/listpage/model"; //审批全部模版数据/{listType}/{orgId}/{corgId}
  static const APPROVEMCURRENTSTATUS =
      "approve/instance/kingdee/status"; // /{approveAssigneeId}/{opinion}1同意 2拒绝
  static const APPROVEMCENTRESHORTCUT =
      "approve/instance/centre/shortcut"; //是否有审批中心快捷入口
  static const KINGDEE_TOKEN = "approve/kingdee/finance/token"; // 获取金蝶审批用户token
}

class MeetingApi {
  static const GET_MEETING_LIST = "meet/meeting/rtc/find/meeting"; //会议列表
  static const GET_MEETING_DETAIL =
      "meet/meeting/invite/find/meeting"; // /{meetingId} 会议详情
  static const MEETING_CANCEL = "meet/meeting/rtc/cancel/meeting"; //取消会议
  static const MEETING_CHECK = "meet/meeting/rtc/check/meeting"; //查询会议是否有密码
  static const GET_MEETING_SIGN = "meet/trc/api/user/sig"; //会议鉴权
  static const MEETING_VERIFY_PWD = "meet/meeting/rtc/into/meeting"; //验证会议密码
  static const GET_MEETING_SETTING =
      "meet/meeting/rtc/permission/meetingId"; //获取会议配置
  static const MEETING_ADD_LIST =
      'meet/meeting/invite/add/meeting'; // /{meetingId} 当前会议添加到当前用户会议列表
  static const MEETING_REMOVE_USER =
      'meet/trc/api/meeting/enter/meetingId'; //查询是否有未结束的会议，进会前调用，后端踢出之前的会议
  static const MEETING_CHECK_OUT =
      "meet/trc/api/meeting/device"; //查看此设备是否有异常退出的会议
  static const MEETING_LEAVE =
      "meet/meeting/rtc/out/meeting/count"; // /{meetingId}退出会议
  static const MEETING_RESERVE = "meet/meeting/rtc/reserve/conference"; // 预约会议
  static const MEETING_QUICK = "meet/meeting/rtc/quick/meeting"; // 快速会议
}

class PanApi {
  static const GET_TOTAL_TOKEN = "pan/file/token/v2"; // 获取所有上传临时秘钥
  static const PAN_FILE_ID = "pan/file/id/v1"; // {hash} 获取上传文件 id
  static const PAN_FILE_CAPACITY =
      "pan/file/capacity/v1"; // {companyId} 获取公司网盘剩余容量

  static const GETFILEPREVIEWURL =
      'https://api.ddbes.com/file/api/v1/preview/getViewUrlWebPath'; //获取查看wps附件的url(所有环境均为此地址)
}

class RuleApi{
  static const RULE_ORG_POWER = "rule/rule/org/power/v1"; // 获取此公司及其子公司集合
  static const RULE_CATEGORY_LIST = "rule/rule/info/phone/list/all"; //规章制度列表
  static const RULE_INFO = "rule/rule/info/phone/info"; // /{id} 规章制度详情
}

enum Env { Product, Dev, Fast, Test, TokenProduct }

class EnvConfig {
  static Env mEnv = Env.Test;

  static switchEnv(Env env) async {
    // 优先使用切换过环境的缓存环境-----------------
    if (Platform.isAndroid) {
      var cacheEnv = await getCurrentEnv();
      if (cacheEnv != null) {
        mEnv = cacheEnv;
      } else {
        mEnv = env;
      }
    }else{
      mEnv = env;
    }

    // 优先使用切换过环境的缓存环境-----------------

    switch (mEnv) {
      case Env.Product:
        Host.HOST = 'https://api.ddbes.com/';
        Host.TOKENHOST = 'https://passport.ddbes.com/';
        Host.ROUTERHOST = 'https://router-im.ddbes.com/';
        Host.WEBHOST = 'https://mobile.ddbes.com';
        Host.SHORTHOST = 'mobile.ddbes.com';
        Host.IM_OFFLINE_HOST = 'https://offline-im.ddbes.com/';

        break;
      case Env.Test:
        Host.HOST = 'https://api.test.ddbes.com/';
        Host.TOKENHOST = 'https://auth.test.ddbes.com/';
        Host.ROUTERHOST = 'http://test.router.ddbes.com:8080/';
        Host.WEBHOST =
            'http://oss.joinu.ltd:9000'; //'http://test.mobile.ddbes.com/';
        Host.SHORTHOST = 'test.mobile.ddbes.com';
        Host.IM_OFFLINE_HOST = 'http://*********:8089/';

        break;
      case Env.Dev:
        // Host.HOST = 'http://api.joinu.ltd/';
        // Host.TOKENHOST = 'http://passport.joinu.ltd/';
        // Host.ROUTERHOST = 'http://router-im.joinu.ltd/';
        // Host.WEBHOST = 'http://test.mobile.ddbes.com/';
        // Host.SHORTHOST = 'mobile.inddbes.com';

        Host.HOST = 'http://*********:8768/';
        Host.TOKENHOST = 'http://*********:9098/';
        Host.ROUTERHOST = 'http://*********:8078/';
        // Host.WEBHOST = 'http://oss.joinu.ltd:9000/';
        Host.WEBHOST = 'http://*********:9000';
        Host.SHORTHOST = 'mobile.inddbes.com';
        Host.IM_OFFLINE_HOST = 'http://*********:8089/';
        break;
      case Env.Test:
        Host.HOST = 'https://api.test.ddbes.com/';
        Host.TOKENHOST = 'https://auth.test.ddbes.com/';
        Host.ROUTERHOST = 'http://test.router.ddbes.com:8080/';
        Host.WEBHOST =
            'http://oss.joinu.ltd:9000'; //'http://test.mobile.ddbes.com/';
        Host.SHORTHOST = 'test.mobile.ddbes.com';
        break;

      case Env.Fast:
        Host.HOST = 'http://*********:8768/';
        Host.TOKENHOST = 'http://*********:9098/';
        Host.ROUTERHOST = 'http://*********:8088/';
        Host.WEBHOST = 'http://test.mobile.ddbes.com';
        Host.SHORTHOST = 'mobile.inddbes.com';
        Host.IM_OFFLINE_HOST = 'http://*********:8089/';

        break;
      case Env.TokenProduct:
        Host.HOST = 'https://api.ddbes.com/';
        Host.TOKENHOST = 'https://passport.ddbes.com/';
        Host.ROUTERHOST = 'https://router-im.ddbes.com/';
        Host.WEBHOST = 'https://mobile.ddbes.com';
        Host.SHORTHOST = 'mobile.ddbes.com';
        Host.IM_OFFLINE_HOST = 'https://offline-im.ddbes.com/';
        break;
      default:
        break;
    }
  }
}
