#!/bin/bash
# 适用于3.27.0

# 初始化记录项目pwd
projectDir=`pwd`
# 获取 flutter sdk
rootFlutter=`which flutter`
# 提取 flutter skd路径
rootDir=${rootFlutter%/*}
# 获取
flutterOldAArVersion="0"
flutterAArVersion="0"


# 自动定位并修改 .android/build.gradle 文件
update_build_gradle() {
    # 自动定位当前脚本所在目录
    local script_dir
    script_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
    # 定位 .android/build.gradle 文件
    local build_gradle_file="$script_dir/.android/build.gradle"
    # 检查文件是否存在
    if [[ ! -f "$build_gradle_file" ]]; then
        echo "Error: .android/build.gradle file not found in $script_dir!"
        return 1
    fi

    # 定义插入的代码段，macOS 的 sed 要求每行以 \\ 结尾
    local insert_code="    subprojects {\\
        afterEvaluate { project ->\\
            if (project.hasProperty('android')) {\\
                project.android {\\
                    if (namespace == null) {\\
                        namespace project.group\\
                    }\\
                }\\
            }\\
        }\\
    }"

    # 检查是否已包含 subprojects 块
    if grep -q "subprojects {" "$build_gradle_file"; then
        echo "The build.gradle file already contains the subprojects block. No changes made."
        return 0
    fi

    # 在 allprojects 的 repositories 块之前插入代码，并确保有空行
    if grep -q "allprojects {" "$build_gradle_file"; then
        sed -i '' "/allprojects {/a\\
$insert_code\\

" "$build_gradle_file"
        echo "The subprojects block has been successfully added with a preceding newline in $build_gradle_file."
    else
        echo "Error: The build.gradle file does not contain an 'allprojects' block!"
        return 1
    fi
}

# 更新aar 版本号
function updateAArVsersion(){
      # 版本号 + 1
    cd ${projectDir}
    v=`grep sonatypeVersion version.txt|cut -d'=' -f2`
    echo 旧版本号$v
    flutterOldAArVersion=$v

    v1=`echo | awk '{split("'$v'",array,"."); print array[1]}'`
    v2=`echo | awk '{split("'$v'",array,"."); print array[2]}'`
    v3=`echo | awk '{split("'$v'",array,"."); print array[3]}'`
    y=`expr $v3 + 1`

    vv=$v1"."$v2"."$y
    echo 新版本号$vv
    flutterAArVersion=$vv
    # 更新配置文件
    sed -i '' 's/sonatypeVersion='$v'/sonatypeVersion='$vv'/g' version.txt
    if [ $? -eq 0 ]; then
        echo ''
    else
        echo '更新版本号失败...'
        exit
    fi
}

# Step1 git pull ,pub get
flutter pub get

# Step2
updateAArVsersion

update_build_gradle

# Step 4 打包aar
echo '执行 flutter build aar --no-profile --no-tree-shake-icons --build-number '$flutterAArVersion
flutter build aar --no-profile --no-tree-shake-icons --build-number $flutterAArVersion

echo '构建 Flutter AAR , 版本：'$flutterOldAArVersion '--->' $flutterAArVersion

# === Maven 配置 ===
VERSION=$flutterAArVersion
REPO_URL="http://**********:8081/repository/maven-releases/"
USERNAME="admin"
PASSWORD="123456"
GROUP_ID="com.joinutech.ddbes"
LOCAL_REPO="build/host/outputs/repo"

# Step 5: 构建 ===
echo "✅ 构建完成，开始上传版本 $VERSION 的产物..."

# Step 6: 上传 Flutter Module 主模块（debug/release）
for VARIANT in flutter_release flutter_debug; do
  MODULE_DIR="$LOCAL_REPO/${GROUP_ID//.//}/$VARIANT/$VERSION"
  if [ -d "$MODULE_DIR" ]; then
    for FILE in "$MODULE_DIR"/*; do
      REL_PATH="${FILE#$LOCAL_REPO/}"
      echo "→ 上传: $REL_PATH"
      curl -u "$USERNAME:$PASSWORD" -T "$FILE" "$REPO_URL$REL_PATH"
    done
  fi
done

# Step 7: 上传 插件产物
find "$LOCAL_REPO" -type d -path "*/$VERSION" | while read DIR; do
  if [[ "$DIR" == *"$GROUP_ID"* ]]; then
    # 已在上面上传过主模块，跳过
    continue
  fi

  files=("$DIR"/*)
  total_files=${#files[@]}
  uploaded_count=0

  for FILE in "$DIR"/*; do
    REL_PATH="${FILE#$LOCAL_REPO/}"
    uploaded_count=$((uploaded_count + 1))
    echo "→ 上传插件产物 ($uploaded_count/$total_files): $REL_PATH"
    curl -u "$USERNAME:$PASSWORD" -T "$FILE" "$REPO_URL$REL_PATH"
  done
done
echo "✅ 所有版本 $VERSION 的构建产物已上传完成。"

# Step 8: 删除本地仓库文件
echo "🧹  删除本地仓库文件: $LOCAL_REPO"
rm -rf "$LOCAL_REPO"

echo "✅  清理完成"